---
description: 
globs: 
alwaysApply: true
---
# 服务端SDK开发指南

## 模块结构

[file-transfer-server-sdk/](mdc:file-transfer-server-sdk) 是服务端 SDK 的核心模块，提供上传、下载、进度查询、速率限制及监控等功能。

## 核心包结构

- **[config/](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/config)**: Spring Boot 自动配置与异步线程池
- **[controller/](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/controller)**: REST API 控制器 (`FileTransferController`, `FileTransferAdminController`)
- **[service/](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/service)**: 业务逻辑层 (`FileTransferService`, `AuthService`, `FileTransferMonitorService`)
- **[entity/](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/entity)**: 数据库实体
- **[mapper/](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/mapper)**: MyBatis Mapper 接口
- **[dto/](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/dto)**: 请求 / 响应对象
- **[interceptor/](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/interceptor)**: 请求认证拦截器 (`AuthInterceptor`)
- **[util/](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/util)**: 工具类

## 主要功能

1. **分块上传**：支持断点续传、秒传，默认分块大小由客户端上报
2. **断点下载**：支持 HTTP Range 请求，实现续传
3. **速率限制**：按用户限速，基于 Token Bucket 算法 (`RateLimitUtils`)
4. **实时进度**：上传 / 下载进度记录在 SQLite 中，可通过 `/filetransfer/api/file/progress/{transferId}` 查询
5. **系统监控**：`FileTransferAdminController` 提供统计、健康检查、速率限制器清理等接口

## 开发指南

### 新增 API
1. 在 `controller` 包创建或修改控制器类
2. 在 `service` 层实现业务逻辑
3. 如需持久化，更新 `entity` / `mapper` 并在 `resources/sql/` 添加迁移脚本
4. 为新接口添加单元 / 集成测试 (`server/test/java`)

### 数据库模型变更
- 修改或新增 `entity`
- 更新对应 `mapper` XML / 注解
- 执行 `resources/sql/init-sqlite.sql` 以保持本地数据库一致

### 配置扩展
- 新增配置字段请修改 `FileTransferProperties` 并在 `application.yml` 提供默认值
- 对于用户级别配置，修改 `UserConfig` 并同步更新 `example-auth-config.yml`

## 依赖管理

所有依赖在 [file-transfer-server-sdk/pom.xml](mdc:file-transfer-server-sdk/pom.xml) 中维护。核心依赖版本由父 `pom.xml` 统一管理。

## 测试

- **单元测试**：位于 `server/test/java`，使用 JUnit 5 + Mockito
- **集成测试**：`FileTransferServiceIntegrationTest` 通过嵌入式容器验证端到端流程
- **性能测试**：`performance/FileTransferPerformanceTest` 提供基准测试脚本

## 运行示例

```bash
# 构建
mvn -pl file-transfer-server-sdk clean package -DskipTests

# 运行（使用 Spring Boot）
java -jar file-transfer-server-sdk/target/file-transfer-server-sdk-*.jar
```

> 若需要完整演示，请运行 [file-transfer-demo](mdc:file-transfer-demo) 模块。
