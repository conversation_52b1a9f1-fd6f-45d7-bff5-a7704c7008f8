package com.sdesrd.filetransfer.client;

import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.dto.ApiResult;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.FileInfo;
import com.sdesrd.filetransfer.client.dto.FileUploadInitRequest;
import com.sdesrd.filetransfer.client.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.AuthUtils;
import com.sdesrd.filetransfer.client.util.FileUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 文件传输客户端
 * 支持断点续传和传输限速
 */
@Slf4j
public class FileTransferClient implements AutoCloseable {
    
    private final ClientConfig config;
    private final OkHttpClient httpClient;
    private final ExecutorService executor;
    
    public FileTransferClient(ClientConfig config) {
        this.config = config;
        this.httpClient = createHttpClient();
        this.executor = Executors.newFixedThreadPool(config.getMaxConcurrentTransfers());
    }
    
    /**
     * 创建HTTP客户端
     */
    private OkHttpClient createHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(config.getConnectTimeoutSeconds()))
                .readTimeout(Duration.ofSeconds(config.getReadTimeoutSeconds()))
                .writeTimeout(Duration.ofSeconds(config.getWriteTimeoutSeconds()))
                .connectionPool(new ConnectionPool(
                        config.getMaxIdleConnections(),
                        config.getKeepAliveDurationMinutes(),
                        TimeUnit.MINUTES
                ))
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 上传文件
     * 
     * @param localFilePath 本地文件路径
     * @param targetName    目标文件名（可选，为空则使用原文件名）
     * @param listener      传输监听器（可选）
     * @return 上传结果
     */
    public CompletableFuture<UploadResult> uploadFile(String localFilePath, String targetName, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return uploadFileSync(localFilePath, targetName, listener);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, executor);
    }
    
    /**
     * 同步上传文件
     */
    public UploadResult uploadFileSync(String localFilePath, String targetName, TransferListener listener) throws FileTransferException {
        File file = new File(localFilePath);
        if (!file.exists() || !file.isFile()) {
            throw new FileTransferException("文件不存在或不是有效文件：" + localFilePath);
        }
        
        try {
            // 通知开始传输
            if (listener != null) {
                TransferProgress startProgress = new TransferProgress();
                startProgress.setFileName(targetName != null ? targetName : file.getName());
                startProgress.setTotalSize(file.length());
                startProgress.setTransferredSize(0L);
                startProgress.setProgress(0.0);
                startProgress.setCompleted(false);
                listener.onStart(startProgress);
            }

            // 计算文件MD5
            String fileMd5 = FileUtils.calculateMD5(file);
            if (targetName == null || targetName.trim().isEmpty()) {
                targetName = file.getName();
            }

            // 初始化上传
            FileUploadInitRequest initRequest = new FileUploadInitRequest();
            initRequest.setFileName(targetName);
            initRequest.setFileSize(file.length());
            initRequest.setFileMd5(fileMd5);
            initRequest.setChunkSize(config.getChunkSize());
            
            ApiResult<FileUploadInitResponse> initResult = initUpload(initRequest);
            if (!initResult.isSuccess()) {
                throw new FileTransferException("初始化上传失败：" + initResult.getMessage());
            }
            
            FileUploadInitResponse initResponse = initResult.getData();
            
            // 如果支持秒传
            if (initResponse.getFastUpload()) {
                log.info("文件秒传成功：{}", targetName);
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setTransferId(initResponse.getTransferId());
                    progress.setFileName(targetName);
                    progress.setTotalSize(file.length());
                    progress.setTransferredSize(file.length());
                    progress.setProgress(100.0);
                    progress.setCompleted(true);
                    listener.onProgress(progress);
                    listener.onCompleted(progress);
                }
                
                UploadResult result = new UploadResult();
                result.setSuccess(true);
                result.setTransferId(initResponse.getTransferId());
                result.setFileId(initResponse.getFileId());
                result.setFileName(targetName);
                result.setFileSize(file.length());
                result.setFastUpload(true);
                return result;
            }
            
            // 分块上传
            return uploadChunks(file, initResponse, listener);
            
        } catch (Exception e) {
            log.error("上传文件失败：{}", localFilePath, e);
            throw new FileTransferException("上传文件失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 下载文件
     * 
     * @param fileId       文件ID
     * @param savePath     保存路径
     * @param listener     传输监听器（可选）
     * @return 下载结果
     */
    public CompletableFuture<DownloadResult> downloadFile(String fileId, String savePath, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return downloadFileSync(fileId, savePath, listener);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, executor);
    }
    
    /**
     * 同步下载文件
     */
    public DownloadResult downloadFileSync(String fileId, String savePath, TransferListener listener) throws FileTransferException {
        try {
            // 通知开始下载
            if (listener != null) {
                TransferProgress startProgress = new TransferProgress();
                // TransferProgress没有setFileId方法，使用fileName代替
                startProgress.setFileName("下载文件-" + fileId);
                startProgress.setTransferredSize(0L);
                startProgress.setProgress(0.0);
                startProgress.setCompleted(false);
                listener.onStart(startProgress);
            }

            String url = config.getServerUrl() + "/filetransfer/api/file/download/" + fileId;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "FileTransferClient/1.0.0");

            // 添加认证头
            addAuthHeaders(requestBuilder);
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new FileTransferException("下载请求失败：" + response.code() + " " + response.message());
                }
                
                // 保存文件
                File saveFile = new File(savePath);
                FileUtils.saveResponseToFile(response, saveFile, listener);
                
                DownloadResult result = new DownloadResult();
                result.setSuccess(true);
                result.setFileId(fileId);
                result.setLocalPath(savePath);
                result.setFileSize(saveFile.length());
                return result;
            }
            
        } catch (Exception e) {
            log.error("下载文件失败：{}", fileId, e);
            throw new FileTransferException("下载文件失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 查询传输进度
     */
    public TransferProgress queryProgress(String transferId) throws FileTransferException {
        try {
            String url = config.getServerUrl() + "/filetransfer/api/file/progress/" + transferId;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "FileTransferClient/1.0.0");
            
            // 添加认证头
            addAuthHeaders(requestBuilder);
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new FileTransferException("查询进度失败：" + response.code() + " " + response.message());
                }
                
                String responseBody = response.body().string();
                ApiResult<TransferProgress> result = JSON.parseObject(responseBody, new TypeReference<ApiResult<TransferProgress>>() {});
                
                if (!result.isSuccess()) {
                    throw new FileTransferException("查询进度失败：" + result.getMessage());
                }
                
                return result.getData();
            }
            
        } catch (Exception e) {
            log.error("查询传输进度失败：{}", transferId, e);
            throw new FileTransferException("查询传输进度失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 获取文件详细信息
     * 
     * @param fileId 文件ID（MD5值）
     * @return 文件信息
     * @throws FileTransferException 如果获取失败
     */
    public FileInfo getFileInfo(String fileId) throws FileTransferException {
        try {
            String url = config.getServerUrl() + "/filetransfer/api/file/download/info/" + fileId;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "FileTransferClient/1.0.0");
            
            // 添加认证头
            addAuthHeaders(requestBuilder);
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new FileTransferException("获取文件信息失败：" + response.code() + " " + response.message());
                }
                
                String responseBody = response.body().string();
                ApiResult<FileInfo> result = JSON.parseObject(responseBody, new TypeReference<ApiResult<FileInfo>>() {});
                
                if (!result.isSuccess()) {
                    throw new FileTransferException("获取文件信息失败：" + result.getMessage());
                }
                
                return result.getData();
            }
            
        } catch (Exception e) {
            log.error("获取文件信息失败：{}", fileId, e);
            throw new FileTransferException("获取文件信息失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 异步获取文件详细信息
     * 
     * @param fileId 文件ID（MD5值）
     * @return 文件信息的CompletableFuture
     */
    public CompletableFuture<FileInfo> getFileInfoAsync(String fileId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getFileInfo(fileId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, executor);
    }
    
    /**
     * 通过相对路径获取文件信息
     * 支持从相对路径获取文件信息，客户端可据此获取fileId进行下载
     * 
     * @param relativePath 相对路径
     * @param listener 传输监听器（可选）
     * @return 文件信息
     * @throws FileTransferException 传输异常
     */
    public FileInfo getFileInfoByPath(String relativePath, TransferListener listener) throws FileTransferException {
        try {
            String url = config.getServerUrl() + "/filetransfer/api/file/file/info/by-path?path=" + java.net.URLEncoder.encode(relativePath, "UTF-8");
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "FileTransferClient/1.0.0");
            
            // 添加认证头
            addAuthHeaders(requestBuilder);
            
            if (listener != null) {
                listener.onStart(new TransferProgress());
            }
            
            try (Response response = httpClient.newCall(requestBuilder.build()).execute()) {
                if (!response.isSuccessful()) {
                    throw new FileTransferException("获取文件信息失败: HTTP " + response.code() + " - " + response.message());
                }
                
                if (response.body() == null) {
                    throw new FileTransferException("响应体为空");
                }
                
                String responseBody = response.body().string();
                ApiResult<FileInfo> result = JSON.parseObject(responseBody, new TypeReference<ApiResult<FileInfo>>() {});
                
                if (!result.isSuccess()) {
                    throw new FileTransferException("获取文件信息失败: " + result.getMessage());
                }
                
                FileInfo fileInfo = result.getData();
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setFileName(fileInfo.getFileName());
                    progress.setTotalSize(fileInfo.getFileSize());
                    progress.setTransferredSize(fileInfo.getFileSize());
                    progress.setProgress(100.0);
                    progress.setCompleted(true);
                    listener.onCompleted(progress);
                }
                
                return fileInfo;
            }
            
        } catch (Exception e) {
            log.error("通过相对路径获取文件信息失败", e);
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setFileName(relativePath);
                listener.onError(progress, e);
            }
            throw new FileTransferException("获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 通过相对路径下载文件（使用fileId方式）
     * 先获取文件信息，然后通过fileId下载
     * 
     * @param relativePath 相对路径
     * @param localPath 本地保存路径
     * @param listener 传输监听器（可选）
     * @return 下载结果Future
     */
    public CompletableFuture<DownloadResult> downloadFileByPathViaFileId(String relativePath, String localPath, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 先获取文件信息
                FileInfo fileInfo = getFileInfoByPath(relativePath, null);
                if (fileInfo == null) {
                    throw new FileTransferException("文件不存在: " + relativePath);
                }
                
                // 使用fileId下载文件
                return downloadFileSync(fileInfo.getFileId(), localPath, listener);
                
            } catch (Exception e) {
                log.error("通过相对路径下载文件失败", e);
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setFileName(relativePath);
                    listener.onError(progress, e);
                }
                
                DownloadResult result = new DownloadResult();
                result.setSuccess(false);
                result.setErrorMessage(e.getMessage());
                result.setLocalPath(localPath);
                return result;
            }
        }, executor);
    }
    
    /**
     * 分块下载文件（支持断点续传）
     * 
     * @param fileId 文件ID
     * @param savePath 保存路径
     * @param listener 传输监听器（可选）
     * @return 下载结果
     */
    public CompletableFuture<DownloadResult> downloadFileChunk(String fileId, String savePath, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return downloadFileChunkSync(fileId, savePath, listener);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, executor);
    }
    
    /**
     * 同步分块下载文件（支持断点续传）
     */
    public DownloadResult downloadFileChunkSync(String fileId, String savePath, TransferListener listener) throws FileTransferException {
        try {
            File saveFile = new File(savePath);
            long startPos = 0;
            
            // 如果文件已存在，从断点位置继续下载
            if (saveFile.exists()) {
                startPos = saveFile.length();
                log.info("检测到断点文件，从位置 {} 继续下载", startPos);
            }
            
            // 通知开始下载
            if (listener != null) {
                TransferProgress startProgress = new TransferProgress();
                startProgress.setFileName("分块下载-" + fileId);
                startProgress.setTransferredSize(startPos);
                startProgress.setProgress(0.0);
                startProgress.setCompleted(false);
                listener.onStart(startProgress);
            }

            String url = config.getServerUrl() + "/filetransfer/api/file/download/chunk/" + fileId;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "FileTransferClient/1.0.0");
            
            // 添加Range头支持断点续传
            if (startPos > 0) {
                requestBuilder.addHeader("Range", "bytes=" + startPos + "-");
            }

            // 添加认证头
            addAuthHeaders(requestBuilder);
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful() && response.code() != 206) { // 206 是部分内容响应
                    throw new FileTransferException("分块下载请求失败：" + response.code() + " " + response.message());
                }
                
                // 保存文件（支持追加模式）
                FileUtils.saveResponseToFileWithResume(response, saveFile, startPos, listener);
                
                DownloadResult result = new DownloadResult();
                result.setSuccess(true);
                result.setFileId(fileId);
                result.setLocalPath(savePath);
                result.setFileSize(saveFile.length());
                return result;
            }
            
        } catch (Exception e) {
            log.error("分块下载文件失败：{}", fileId, e);
            throw new FileTransferException("分块下载文件失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 初始化上传
     */
    private ApiResult<FileUploadInitResponse> initUpload(FileUploadInitRequest request) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/file/upload/init";
        String json = JSON.toJSONString(request);
        
        RequestBody body = RequestBody.create(json, MediaType.get("application/json; charset=utf-8"));
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("User-Agent", "FileTransferClient/1.0.0");
        
        // 添加认证头
        addAuthHeaders(requestBuilder);
        
        try (Response response = httpClient.newCall(requestBuilder.build()).execute()) {
            String responseBody = response.body().string();
            return JSON.parseObject(responseBody, new TypeReference<ApiResult<FileUploadInitResponse>>() {});
        }
    }
    
    /**
     * 分块上传
     */
    private UploadResult uploadChunks(File file, FileUploadInitResponse initResponse, TransferListener listener) throws Exception {
        long chunkSize = config.getChunkSize();
        long fileSize = file.length();
        int totalChunks = (int) Math.ceil((double) fileSize / chunkSize);

        // 通知开始传输（如果还没有通知过）
        if (listener != null) {
            TransferProgress startProgress = new TransferProgress();
            startProgress.setTransferId(initResponse.getTransferId());
            startProgress.setFileName(initResponse.getFileName());
            startProgress.setTotalSize(fileSize);
            startProgress.setTransferredSize(0L);
            startProgress.setProgress(0.0);
            startProgress.setTotalChunks(totalChunks);
            startProgress.setCompletedChunks(0);
            startProgress.setCompleted(false);
            listener.onStart(startProgress);
        }

        // 获取已上传的分块
        TransferProgress progress = queryProgress(initResponse.getTransferId());
        int startChunk = progress.getCompletedChunks();
        
        // 从断点继续上传
        for (int chunkIndex = startChunk; chunkIndex < totalChunks; chunkIndex++) {
            long offset = (long) chunkIndex * chunkSize;
            long currentChunkSize = Math.min(chunkSize, fileSize - offset);
            
            // 读取分块数据
            byte[] chunkData = FileUtils.readFileChunk(file, offset, currentChunkSize);
            String chunkMd5 = FileUtils.calculateMD5(chunkData);
            
            // 上传分块
            uploadChunk(initResponse.getTransferId(), chunkIndex, chunkData, chunkMd5);
            
            // 更新进度
            if (listener != null) {
                TransferProgress currentProgress = new TransferProgress();
                currentProgress.setTransferId(initResponse.getTransferId());
                currentProgress.setFileName(initResponse.getFileName());
                currentProgress.setTotalSize(fileSize);
                currentProgress.setTransferredSize(offset + currentChunkSize);
                currentProgress.setProgress((double) (offset + currentChunkSize) / fileSize * 100);
                currentProgress.setTotalChunks(totalChunks);
                currentProgress.setCompletedChunks(chunkIndex + 1);
                currentProgress.setCompleted(chunkIndex + 1 >= totalChunks);
                
                listener.onProgress(currentProgress);
                
                if (currentProgress.isCompleted()) {
                    listener.onCompleted(currentProgress);
                }
            }
        }
        
        // 完成上传
        completeUpload(initResponse.getTransferId());
        
        UploadResult result = new UploadResult();
        result.setSuccess(true);
        result.setTransferId(initResponse.getTransferId());
        result.setFileId(initResponse.getFileId());
        result.setFileName(initResponse.getFileName());
        result.setFileSize(fileSize);
        result.setFastUpload(false);
        return result;
    }
    
    /**
     * 上传单个分块
     */
    private void uploadChunk(String transferId, int chunkIndex, byte[] chunkData, String chunkMd5) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/file/upload/chunk";
        
        RequestBody formBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("transferId", transferId)
                .addFormDataPart("chunkIndex", String.valueOf(chunkIndex))
                .addFormDataPart("chunkMd5", chunkMd5)
                .addFormDataPart("chunk", "chunk", RequestBody.create(chunkData, MediaType.get("application/octet-stream")))
                .build();
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(formBody)
                .addHeader("User-Agent", "FileTransferClient/1.0.0");
        
        // 添加认证头
        addAuthHeaders(requestBuilder);
        
        Request request = requestBuilder.build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("分块上传失败：" + response.code() + " " + response.message());
            }
            
            String responseBody = response.body().string();
            ApiResult<?> result = JSON.parseObject(responseBody, ApiResult.class);
            if (!result.isSuccess()) {
                throw new IOException("分块上传失败：" + result.getMessage());
            }
        }
    }
    
    /**
     * 完成上传
     */
    private void completeUpload(String transferId) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/file/upload/complete/" + transferId;
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(RequestBody.create("", MediaType.get("application/json")))
                .addHeader("User-Agent", "FileTransferClient/1.0.0");
        
        // 添加认证头
        addAuthHeaders(requestBuilder);
        
        Request request = requestBuilder.build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("完成上传失败：" + response.code() + " " + response.message());
            }
            
            String responseBody = response.body().string();
            ApiResult<?> result = JSON.parseObject(responseBody, ApiResult.class);
            if (!result.isSuccess()) {
                throw new IOException("完成上传失败：" + result.getMessage());
            }
        }
    }
    
    /**
     * 添加认证头
     */
    private void addAuthHeaders(Request.Builder requestBuilder) {
        String username = config.getAuth().getUser();
        String secretKey = config.getAuth().getSecretKey();
        String authToken = AuthUtils.generateAuthToken(username, secretKey);
        
        requestBuilder.addHeader(AuthUtils.USER_HEADER, username);
        requestBuilder.addHeader(AuthUtils.AUTH_HEADER, authToken);
    }
    
    /**
     * 关闭客户端
     */
    public void close() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
        }
    }
} 