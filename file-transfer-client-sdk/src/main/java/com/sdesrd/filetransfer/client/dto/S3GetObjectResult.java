package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * S3下载对象结果DTO
 */
@Data
public class S3GetObjectResult {
    
    /**
     * 对象键
     */
    private String key;
    
    /**
     * 本地文件路径
     */
    private String localPath;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * ETag值
     */
    private String etag;
} 