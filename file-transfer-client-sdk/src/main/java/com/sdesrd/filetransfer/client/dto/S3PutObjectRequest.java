package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * S3风格的上传对象请求DTO
 */
@Data
public class S3PutObjectRequest {
    
    /**
     * 对象键（相对路径）
     */
    private String key;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 内容MD5
     */
    private String contentMd5;
    
    /**
     * 是否启用分片上传
     */
    private Boolean multipart;
    
    /**
     * 分片大小
     */
    private Integer chunkSize;
    
    /**
     * 存储类别
     */
    private String storageClass;
    
    /**
     * 元数据
     */
    private String metadata;
    
    /**
     * 标签
     */
    private String tags;
    
    /**
     * 是否覆盖已存在的文件
     */
    private Boolean overwrite;
} 