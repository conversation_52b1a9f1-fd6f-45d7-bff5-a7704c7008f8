package com.sdesrd.filetransfer.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 文件传输SDK演示应用
 * 
 * 这个应用演示了如何集成和使用文件传输服务端SDK
 * 支持传统的fileId模式和S3风格的key-value模式
 */
@SpringBootApplication(scanBasePackages = {"com.sdesrd.filetransfer"})
public class FileTransferDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(FileTransferDemoApplication.class, args);
        System.out.println("===========================================");
        System.out.println("文件传输SDK演示应用启动成功！");
        System.out.println("");
        System.out.println("🌐 API文档地址:");
        System.out.println("   http://localhost:49011/doc.html");
        System.out.println("");
        System.out.println("📡 API端点:");
        System.out.println("   传统模式: http://localhost:49011/filetransfer/api/file/*");
        System.out.println("   S3风格:   http://localhost:49011/filetransfer/api/s3/*");
        System.out.println("   管理接口: http://localhost:49011/filetransfer/api/admin/*");
        System.out.println("");
        System.out.println("💡 主要特性:");
        System.out.println("   ✅ 支持断点续传和分片上传下载");
        System.out.println("   ✅ 基于用户的差异化限速控制");
        System.out.println("   ✅ S3风格的key-value对象存储");
        System.out.println("   ✅ 文件MD5校验和秒传功能");
        System.out.println("   ✅ 实时传输进度监控");
        System.out.println("");
        System.out.println("🔑 S3风格API示例:");
        System.out.println("   PUT    /filetransfer/api/s3/documents/file.pdf");
        System.out.println("   GET    /filetransfer/api/s3/documents/file.pdf");
        System.out.println("   DELETE /filetransfer/api/s3/documents/file.pdf");
        System.out.println("   GET    /filetransfer/api/s3/?prefix=documents/");
        System.out.println("===========================================");
    }
} 