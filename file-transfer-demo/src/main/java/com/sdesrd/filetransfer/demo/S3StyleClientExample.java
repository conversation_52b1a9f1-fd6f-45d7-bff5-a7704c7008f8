package com.sdesrd.filetransfer.demo;

import java.nio.file.Files;
import java.nio.file.Paths;

import org.springframework.stereotype.Component;

import com.sdesrd.filetransfer.client.S3FileTransferClient;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.dto.S3GetObjectResult;
import com.sdesrd.filetransfer.client.dto.S3ListObjectsResponse;
import com.sdesrd.filetransfer.client.dto.S3ListObjectsResult;
import com.sdesrd.filetransfer.client.dto.S3ObjectInfo;
import com.sdesrd.filetransfer.client.dto.S3PutObjectResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.listener.TransferListener;

import lombok.extern.slf4j.Slf4j;

/**
 * S3风格客户端使用示例
 */
@Slf4j
@Component
public class S3StyleClientExample {
    
    public void runS3StyleDemo() {
        log.info("=== S3风格文件传输客户端演示开始 ===");
        
        // 1. 创建客户端配置
        ClientConfig config = new ClientConfig();
        config.getAuth().setServerAddr("localhost");
        config.getAuth().setServerPort(49011);
        config.getAuth().setUser("user1");
        config.getAuth().setSecretKey("user1-secret-2024");
        
        // 2. 创建S3风格客户端
        try (S3FileTransferClient s3Client = new S3FileTransferClient(config)) {
            
            // 创建传输监听器
            TransferListener listener = new TransferListener() {
                @Override
                public void onProgress(TransferProgress progress) {
                    System.out.printf("传输进度: %.2f%% - %s (%s/%s)%n", 
                        progress.getProgress(),
                        progress.getFileName(),
                        formatSize(progress.getTransferredSize()),
                        formatSize(progress.getTotalSize()));
                }
                
                @Override
                public void onCompleted(TransferProgress progress) {
                    System.out.println("✅ 传输完成: " + progress.getFileName());
                }
                
                @Override
                public void onError(TransferProgress progress, Throwable error) {
                    System.err.println("❌ 传输失败: " + error.getMessage());
                }
            };
            
            // 3. 演示上传文件
            demonstrateUpload(s3Client, listener);
            
            // 4. 演示列出对象
            demonstrateListObjects(s3Client);
            
            // 5. 演示获取对象信息
            demonstrateGetObjectInfo(s3Client);
            
            // 6. 演示下载文件
            demonstrateDownload(s3Client, listener);
            
            // 7. 演示删除对象
            demonstrateDeleteObject(s3Client);
            
        } catch (Exception e) {
            log.error("S3风格演示失败", e);
        }
        
        log.info("=== S3风格文件传输客户端演示结束 ===");
    }
    
    /**
     * 演示上传文件
     */
    private void demonstrateUpload(S3FileTransferClient s3Client, TransferListener listener) {
        try {
            log.info("\n--- 演示文件上传 ---");
            
            // 创建测试文件
            String testContent = "这是一个S3风格API的测试文件\n当前时间: " + System.currentTimeMillis();
            String localFilePath = "./test-s3-upload.txt";
            Files.write(Paths.get(localFilePath), testContent.getBytes());
            
            // 上传到不同的"目录"路径
            String[] testKeys = {
                "documents/readme.txt",
                "images/photos/vacation.jpg",
                "projects/2024/file-transfer/test.txt",
                "temp/test-file.txt"
            };
            
            for (String key : testKeys) {
                log.info("上传文件到: {}", key);
                
                // 同步上传
                S3PutObjectResult result = s3Client.putObject(key, localFilePath, listener);
                
                if (result.getSuccess()) {
                    log.info("✅ 上传成功 - Key: {}, ETag: {}", result.getKey(), result.getEtag());
                } else {
                    log.error("❌ 上传失败 - Key: {}, Error: {}", key, result.getErrorMessage());
                }
            }
            
            // 清理测试文件
            Files.deleteIfExists(Paths.get(localFilePath));
            
        } catch (Exception e) {
            log.error("上传演示失败", e);
        }
    }
    
    /**
     * 演示列出对象
     */
    private void demonstrateListObjects(S3FileTransferClient s3Client) {
        try {
            log.info("\n--- 演示列出对象 ---");
            
            // 列出所有对象
            log.info("列出所有对象:");
            S3ListObjectsResult allResult = s3Client.listObjects(null, "/", 100);
            if (allResult.getSuccess()) {
                printListResult(allResult.getListObjectsResponse());
            }
            
            // 列出特定前缀的对象
            log.info("\n列出 'documents/' 前缀的对象:");
            S3ListObjectsResult documentsResult = s3Client.listObjects("documents/", "/", 10);
            if (documentsResult.getSuccess()) {
                printListResult(documentsResult.getListObjectsResponse());
            }
            
            // 列出特定前缀但不使用分隔符（平铺所有文件）
            log.info("\n列出 'projects/' 前缀的所有文件（平铺）:");
            S3ListObjectsResult projectsResult = s3Client.listObjects("projects/", null, 10);
            if (projectsResult.getSuccess()) {
                printListResult(projectsResult.getListObjectsResponse());
            }
            
        } catch (Exception e) {
            log.error("列出对象演示失败", e);
        }
    }
    
    /**
     * 打印列表结果
     */
    private void printListResult(S3ListObjectsResponse response) {
        if (response.getCommonPrefixes() != null && !response.getCommonPrefixes().isEmpty()) {
            System.out.println("📁 目录:");
            for (String prefix : response.getCommonPrefixes()) {
                System.out.println("  " + prefix);
            }
        }
        
        if (response.getContents() != null && !response.getContents().isEmpty()) {
            System.out.println("📄 文件:");
            for (S3ObjectInfo obj : response.getContents()) {
                System.out.printf("  %s (%s) - %s%n", 
                    obj.getKey(), 
                    obj.getFormattedSize(), 
                    obj.getLastModified());
            }
        }
        
        System.out.printf("总计: %d 个对象%n", response.getKeyCount());
    }
    
    /**
     * 演示获取对象信息
     */
    private void demonstrateGetObjectInfo(S3FileTransferClient s3Client) {
        try {
            log.info("\n--- 演示获取对象信息 ---");
            
            String key = "documents/readme.txt";
            log.info("获取对象信息: {}", key);
            
            S3ObjectInfo objectInfo = s3Client.getObjectInfo(key);
            if (objectInfo != null) {
                System.out.printf("对象信息:%n");
                System.out.printf("  Key: %s%n", objectInfo.getKey());
                System.out.printf("  大小: %s (%d bytes)%n", objectInfo.getFormattedSize(), objectInfo.getSize());
                System.out.printf("  类型: %s%n", objectInfo.getContentType());
                System.out.printf("  ETag: %s%n", objectInfo.getEtag());
                System.out.printf("  最后修改: %s%n", objectInfo.getLastModified());
                System.out.printf("  创建时间: %s%n", objectInfo.getCreatedTime());
            } else {
                log.warn("对象不存在: {}", key);
            }
            
        } catch (Exception e) {
            log.error("获取对象信息演示失败", e);
        }
    }
    
    /**
     * 演示下载文件
     */
    private void demonstrateDownload(S3FileTransferClient s3Client, TransferListener listener) {
        try {
            log.info("\n--- 演示文件下载 ---");
            
            String key = "documents/readme.txt";
            String localPath = "./downloaded-" + System.currentTimeMillis() + ".txt";
            
            log.info("下载文件: {} -> {}", key, localPath);
            
            // 同步下载
            S3GetObjectResult result = s3Client.getObject(key, localPath, listener);
            
            if (result.getSuccess()) {
                log.info("✅ 下载成功 - Key: {}, 本地路径: {}", result.getKey(), result.getLocalPath());
                
                // 读取并显示文件内容
                String content = new String(Files.readAllBytes(Paths.get(localPath)));
                System.out.println("文件内容:\n" + content);
                
                // 清理下载的文件
                Files.deleteIfExists(Paths.get(localPath));
            } else {
                log.error("❌ 下载失败 - Key: {}, Error: {}", key, result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("下载演示失败", e);
        }
    }
    
    /**
     * 演示删除对象
     */
    private void demonstrateDeleteObject(S3FileTransferClient s3Client) {
        try {
            log.info("\n--- 演示删除对象 ---");
            
            String keyToDelete = "temp/test-file.txt";
            log.info("删除对象: {}", keyToDelete);
            
            boolean deleted = s3Client.deleteObject(keyToDelete);
            if (deleted) {
                log.info("✅ 删除成功: {}", keyToDelete);
            } else {
                log.warn("⚠️  对象不存在或删除失败: {}", keyToDelete);
            }
            
        } catch (Exception e) {
            log.error("删除对象演示失败", e);
        }
    }
    
    /**
     * 格式化文件大小
     */
    private String formatSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024));
        return String.format("%.1f GB", size / (1024.0 * 1024 * 1024));
    }
} 