package com.sdesrd.filetransfer.server.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 文件传输服务配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "file.transfer.server")
public class FileTransferProperties {
    
    /**
     * 是否启用文件传输服务
     */
    private boolean enabled = true;
    
    /**
     * 数据库文件路径
     */
    private String databasePath = "./data/file-transfer/database.db";
    
    /**
     * 清理间隔时间（毫秒）
     */
    private long cleanupInterval = 3600000L; // 1小时
    
    /**
     * 记录过期时间（毫秒）
     */
    private long recordExpireTime = 86400000L; // 24小时
    
    /**
     * 存储桶配置
     */
    private Map<String, BucketConfig> buckets = new HashMap<>();
    
    /**
     * 用户配置
     */
    private Map<String, UserConfig> users = new HashMap<>();
    
    /**
     * 存储桶配置
     */
    @Data
    public static class BucketConfig {
        /**
         * 存储桶名称
         */
        private String name;
        
        /**
         * 存储路径
         */
        private String storagePath;
        
        /**
         * 上传速率限制（字节/秒）
         */
        private long uploadRateLimit = 10485760L; // 10MB/s
        
        /**
         * 下载速率限制（字节/秒）
         */
        private long downloadRateLimit = 10485760L; // 10MB/s
        
        /**
         * 默认分块大小
         */
        private int defaultChunkSize = 2097152; // 2MB
        
        /**
         * 最大文件大小
         */
        private long maxFileSize = 104857600L; // 100MB
        
        /**
         * 最大内存使用大小
         */
        private long maxInMemorySize = 10485760L; // 10MB
        
        /**
         * 是否启用秒传功能
         */
        private boolean fastUploadEnabled = true;
        
        /**
         * 是否启用速率限制
         */
        private boolean rateLimitEnabled = true;
    }
    
    /**
     * 用户配置
     */
    @Data
    public static class UserConfig {
        /**
         * 用户密钥
         */
        private String secretKey;
        
        /**
         * 允许访问的存储桶列表
         */
        private String[] allowedBuckets;
        
        /**
         * 用户级别的上传速率限制（可选，覆盖存储桶设置）
         */
        private Long uploadRateLimit;
        
        /**
         * 用户级别的下载速率限制（可选，覆盖存储桶设置）
         */
        private Long downloadRateLimit;
        
        /**
         * 用户级别的最大文件大小（可选，覆盖存储桶设置）
         */
        private Long maxFileSize;
        
        /**
         * 是否启用速率限制
         */
        private Boolean rateLimitEnabled;
    }
    
    /**
     * 获取用户配置
     */
    public UserConfig getUserConfig(String username) {
        return users.get(username);
    }
    
    /**
     * 获取存储桶配置
     */
    public BucketConfig getBucketConfig(String bucketName) {
        return buckets.get(bucketName);
    }
    
    /**
     * 检查用户是否有权访问指定存储桶
     */
    public boolean isUserAllowedToBucket(String username, String bucketName) {
        UserConfig userConfig = getUserConfig(username);
        if (userConfig == null || userConfig.getAllowedBuckets() == null) {
            return false;
        }
        
        for (String allowedBucket : userConfig.getAllowedBuckets()) {
            if (allowedBucket.equals(bucketName)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取用户在指定存储桶的有效配置（用户配置优先于存储桶配置）
     */
    public EffectiveConfig getEffectiveConfig(String username, String bucketName) {
        UserConfig userConfig = getUserConfig(username);
        BucketConfig bucketConfig = getBucketConfig(bucketName);
        
        if (bucketConfig == null) {
            throw new IllegalArgumentException("存储桶不存在: " + bucketName);
        }
        
        EffectiveConfig effectiveConfig = new EffectiveConfig();
        effectiveConfig.setStoragePath(bucketConfig.getStoragePath());
        effectiveConfig.setDefaultChunkSize(bucketConfig.getDefaultChunkSize());
        effectiveConfig.setMaxInMemorySize(bucketConfig.getMaxInMemorySize());
        effectiveConfig.setFastUploadEnabled(bucketConfig.isFastUploadEnabled());
        
        // 用户配置覆盖存储桶配置
        if (userConfig != null) {
            effectiveConfig.setUploadRateLimit(
                userConfig.getUploadRateLimit() != null ? userConfig.getUploadRateLimit() : bucketConfig.getUploadRateLimit()
            );
            effectiveConfig.setDownloadRateLimit(
                userConfig.getDownloadRateLimit() != null ? userConfig.getDownloadRateLimit() : bucketConfig.getDownloadRateLimit()
            );
            effectiveConfig.setMaxFileSize(
                userConfig.getMaxFileSize() != null ? userConfig.getMaxFileSize() : bucketConfig.getMaxFileSize()
            );
            effectiveConfig.setRateLimitEnabled(
                userConfig.getRateLimitEnabled() != null ? userConfig.getRateLimitEnabled() : bucketConfig.isRateLimitEnabled()
            );
        } else {
            effectiveConfig.setUploadRateLimit(bucketConfig.getUploadRateLimit());
            effectiveConfig.setDownloadRateLimit(bucketConfig.getDownloadRateLimit());
            effectiveConfig.setMaxFileSize(bucketConfig.getMaxFileSize());
            effectiveConfig.setRateLimitEnabled(bucketConfig.isRateLimitEnabled());
        }
        
        return effectiveConfig;
    }
    
    /**
     * 有效配置类
     */
    @Data
    public static class EffectiveConfig {
        private String storagePath;
        private long uploadRateLimit;
        private long downloadRateLimit;
        private int defaultChunkSize;
        private long maxFileSize;
        private long maxInMemorySize;
        private boolean fastUploadEnabled;
        private boolean rateLimitEnabled;
    }

    /**
     * 获取默认配置（用于向后兼容）
     * 从第一个存储桶配置中获取默认值
     *
     * @return 默认配置
     */
    public BucketConfig getDefaultConfig() {
        if (buckets.isEmpty()) {
            // 如果没有配置存储桶，返回一个默认的配置
            BucketConfig defaultConfig = new BucketConfig();
            defaultConfig.setName("default");
            defaultConfig.setStoragePath("./data/file-transfer/files");
            defaultConfig.setUploadRateLimit(10485760L); // 10MB/s
            defaultConfig.setDownloadRateLimit(10485760L); // 10MB/s
            defaultConfig.setDefaultChunkSize(2097152); // 2MB
            defaultConfig.setMaxFileSize(104857600L); // 100MB
            defaultConfig.setMaxInMemorySize(10485760L); // 10MB
            defaultConfig.setFastUploadEnabled(true);
            defaultConfig.setRateLimitEnabled(true);
            return defaultConfig;
        }

        // 返回第一个存储桶配置作为默认配置
        return buckets.values().iterator().next();
    }

    /**
     * 验证配置的完整性和有效性
     *
     * @throws IllegalStateException 如果配置无效
     */
    public void validateConfiguration() {
        // 验证存储桶配置
        if (buckets.isEmpty()) {
            throw new IllegalStateException("至少需要配置一个存储桶");
        }

        for (Map.Entry<String, BucketConfig> entry : buckets.entrySet()) {
            String bucketName = entry.getKey();
            BucketConfig config = entry.getValue();

            if (config.getName() == null || config.getName().trim().isEmpty()) {
                throw new IllegalStateException("存储桶名称不能为空: " + bucketName);
            }

            if (config.getStoragePath() == null || config.getStoragePath().trim().isEmpty()) {
                throw new IllegalStateException("存储桶存储路径不能为空: " + bucketName);
            }

            if (config.getMaxFileSize() <= 0) {
                throw new IllegalStateException("最大文件大小必须大于0: " + bucketName);
            }

            if (config.getDefaultChunkSize() <= 0) {
                throw new IllegalStateException("默认分块大小必须大于0: " + bucketName);
            }
        }

        // 验证用户配置
        if (users.isEmpty()) {
            throw new IllegalStateException("至少需要配置一个用户");
        }

        for (Map.Entry<String, UserConfig> entry : users.entrySet()) {
            String username = entry.getKey();
            UserConfig config = entry.getValue();

            if (config.getSecretKey() == null || config.getSecretKey().trim().isEmpty()) {
                throw new IllegalStateException("用户密钥不能为空: " + username);
            }

            if (config.getAllowedBuckets() == null || config.getAllowedBuckets().length == 0) {
                throw new IllegalStateException("用户必须至少有一个允许访问的存储桶: " + username);
            }

            // 验证用户允许的存储桶是否都存在
            for (String allowedBucket : config.getAllowedBuckets()) {
                if (!buckets.containsKey(allowedBucket)) {
                    throw new IllegalStateException("用户 " + username + " 配置的存储桶不存在: " + allowedBucket);
                }
            }
        }
    }
}