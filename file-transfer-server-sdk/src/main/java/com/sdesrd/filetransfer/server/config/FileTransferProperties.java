package com.sdesrd.filetransfer.server.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 文件传输服务配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "file.transfer.server")
public class FileTransferProperties {
    
    /**
     * 是否启用文件传输服务
     */
    private boolean enabled = true;
    
    /**
     * 数据库文件路径
     */
    private String databasePath = "./data/file-transfer/database.db";
    
    /**
     * 清理间隔时间（毫秒）
     */
    private long cleanupInterval = 3600000L; // 1小时
    
    /**
     * 记录过期时间（毫秒）
     */
    private long recordExpireTime = 86400000L; // 24小时
    
    /**
     * 存储桶配置
     */
    private Map<String, BucketConfig> buckets = new HashMap<>();
    
    /**
     * 用户配置
     */
    private Map<String, UserConfig> users = new HashMap<>();
    
    /**
     * 存储桶配置
     */
    @Data
    public static class BucketConfig {
        /**
         * 存储桶名称
         */
        private String name;
        
        /**
         * 存储路径
         */
        private String storagePath;
        
        /**
         * 上传速率限制（字节/秒）
         */
        private long uploadRateLimit = 10485760L; // 10MB/s
        
        /**
         * 下载速率限制（字节/秒）
         */
        private long downloadRateLimit = 10485760L; // 10MB/s
        
        /**
         * 默认分块大小
         */
        private int defaultChunkSize = 2097152; // 2MB
        
        /**
         * 最大文件大小
         */
        private long maxFileSize = 104857600L; // 100MB
        
        /**
         * 最大内存使用大小
         */
        private long maxInMemorySize = 10485760L; // 10MB
        
        /**
         * 是否启用秒传功能
         */
        private boolean fastUploadEnabled = true;
        
        /**
         * 是否启用速率限制
         */
        private boolean rateLimitEnabled = true;
    }
    
    /**
     * 用户配置
     */
    @Data
    public static class UserConfig {
        /**
         * 用户密钥
         */
        private String secretKey;
        
        /**
         * 允许访问的存储桶列表
         */
        private String[] allowedBuckets;
        
        /**
         * 用户级别的上传速率限制（可选，覆盖存储桶设置）
         */
        private Long uploadRateLimit;
        
        /**
         * 用户级别的下载速率限制（可选，覆盖存储桶设置）
         */
        private Long downloadRateLimit;
        
        /**
         * 用户级别的最大文件大小（可选，覆盖存储桶设置）
         */
        private Long maxFileSize;
        
        /**
         * 是否启用速率限制
         */
        private Boolean rateLimitEnabled;
    }
    
    /**
     * 获取用户配置
     */
    public UserConfig getUserConfig(String username) {
        return users.get(username);
    }
    
    /**
     * 获取存储桶配置
     */
    public BucketConfig getBucketConfig(String bucketName) {
        return buckets.get(bucketName);
    }
    
    /**
     * 检查用户是否有权访问指定存储桶
     */
    public boolean isUserAllowedToBucket(String username, String bucketName) {
        UserConfig userConfig = getUserConfig(username);
        if (userConfig == null || userConfig.getAllowedBuckets() == null) {
            return false;
        }
        
        for (String allowedBucket : userConfig.getAllowedBuckets()) {
            if (allowedBucket.equals(bucketName)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取用户在指定存储桶的有效配置（用户配置优先于存储桶配置）
     */
    public EffectiveConfig getEffectiveConfig(String username, String bucketName) {
        UserConfig userConfig = getUserConfig(username);
        BucketConfig bucketConfig = getBucketConfig(bucketName);
        
        if (bucketConfig == null) {
            throw new IllegalArgumentException("存储桶不存在: " + bucketName);
        }
        
        EffectiveConfig effectiveConfig = new EffectiveConfig();
        effectiveConfig.setStoragePath(bucketConfig.getStoragePath());
        effectiveConfig.setDefaultChunkSize(bucketConfig.getDefaultChunkSize());
        effectiveConfig.setMaxInMemorySize(bucketConfig.getMaxInMemorySize());
        effectiveConfig.setFastUploadEnabled(bucketConfig.isFastUploadEnabled());
        
        // 用户配置覆盖存储桶配置
        if (userConfig != null) {
            effectiveConfig.setUploadRateLimit(
                userConfig.getUploadRateLimit() != null ? userConfig.getUploadRateLimit() : bucketConfig.getUploadRateLimit()
            );
            effectiveConfig.setDownloadRateLimit(
                userConfig.getDownloadRateLimit() != null ? userConfig.getDownloadRateLimit() : bucketConfig.getDownloadRateLimit()
            );
            effectiveConfig.setMaxFileSize(
                userConfig.getMaxFileSize() != null ? userConfig.getMaxFileSize() : bucketConfig.getMaxFileSize()
            );
            effectiveConfig.setRateLimitEnabled(
                userConfig.getRateLimitEnabled() != null ? userConfig.getRateLimitEnabled() : bucketConfig.isRateLimitEnabled()
            );
        } else {
            effectiveConfig.setUploadRateLimit(bucketConfig.getUploadRateLimit());
            effectiveConfig.setDownloadRateLimit(bucketConfig.getDownloadRateLimit());
            effectiveConfig.setMaxFileSize(bucketConfig.getMaxFileSize());
            effectiveConfig.setRateLimitEnabled(bucketConfig.isRateLimitEnabled());
        }
        
        return effectiveConfig;
    }
    
    /**
     * 有效配置类
     */
    @Data
    public static class EffectiveConfig {
        private String storagePath;
        private long uploadRateLimit;
        private long downloadRateLimit;
        private int defaultChunkSize;
        private long maxFileSize;
        private long maxInMemorySize;
        private boolean fastUploadEnabled;
        private boolean rateLimitEnabled;
    }
} 