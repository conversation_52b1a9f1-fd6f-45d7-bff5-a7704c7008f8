package com.sdesrd.filetransfer.server.controller;

import com.sdesrd.filetransfer.server.dto.*;
import com.sdesrd.filetransfer.server.interceptor.AuthInterceptor;
import com.sdesrd.filetransfer.server.service.S3StyleFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * S3风格文件传输控制器
 * 提供类似AWS S3的REST API接口，支持存储桶概念
 */
@Slf4j
@RestController
@RequestMapping("/filetransfer/api/s3")
public class S3StyleFileController {
    
    @Autowired
    private S3StyleFileService s3StyleFileService;
    
    /**
     * 初始化对象上传
     * POST /filetransfer/api/s3/upload/init
     */
    @PostMapping("/upload/init")
    public ApiResult<FileUploadInitResponse> initUpload(
            @RequestBody S3PutObjectRequest request,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("初始化上传 - 用户: {}, 存储桶: {}, key: {}, 文件: {}, IP: {}", 
                    username, request.getBucket(), request.getKey(), request.getFileName(), clientIp);
            
            FileUploadInitResponse response = s3StyleFileService.initUpload(request, username, clientIp);
            return ApiResult.success(response);
            
        } catch (Exception e) {
            log.error("初始化上传失败", e);
            return ApiResult.error(500, e.getMessage());
        }
    }
    
    /**
     * 上传文件分块
     * POST /filetransfer/api/s3/upload/chunk
     */
    @PostMapping("/upload/chunk")
    public ApiResult<String> uploadChunk(
            @RequestParam("transferId") String transferId,
            @RequestParam("chunkIndex") Integer chunkIndex,
            @RequestParam("chunkMd5") String chunkMd5,
            @RequestParam("chunk") MultipartFile chunk,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            log.debug("上传分块 - 用户: {}, 传输ID: {}, 分块: {}, 大小: {}", 
                    username, transferId, chunkIndex, chunk.getSize());
            
            s3StyleFileService.uploadChunk(transferId, chunkIndex, chunkMd5, chunk, username);
            return ApiResult.success("分块上传成功");
            
        } catch (Exception e) {
            log.error("上传分块失败 - 传输ID: {}, 分块: {}", transferId, chunkIndex, e);
            return ApiResult.error(500, e.getMessage());
        }
    }
    
    /**
     * 完成对象上传
     * POST /filetransfer/api/s3/upload/complete
     */
    @PostMapping("/upload/complete")
    public ApiResult<Map<String, Object>> completeUpload(
            @RequestParam("transferId") String transferId,
            @RequestParam("bucket") String bucket,
            @RequestParam("key") String key,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            log.info("完成上传 - 用户: {}, 传输ID: {}, 存储桶: {}, key: {}", 
                    username, transferId, bucket, key);
            
            Map<String, Object> result = s3StyleFileService.completeUpload(transferId, bucket, key, username);
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("完成上传失败 - 传输ID: {}, key: {}", transferId, key, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 下载对象
     * GET /filetransfer/api/s3/{bucket}/{key}
     */
    @GetMapping("/{bucket}/{key:.*}")
    public ResponseEntity<Resource> getObject(
            @PathVariable String bucket,
            @PathVariable String key,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            Resource resource = s3StyleFileService.getObject(bucket, key, username);
            S3ObjectInfo objectInfo = s3StyleFileService.getObjectInfo(bucket, key, username);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + objectInfo.getFileName() + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, objectInfo.getContentType())
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(objectInfo.getSize()))
                    .header("ETag", objectInfo.getEtag())
                    .body(resource);
        } catch (Exception e) {
            log.error("下载对象失败: bucket={}, key={}", bucket, key, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    /**
     * 获取对象元数据
     * HEAD /filetransfer/api/s3/{bucket}/{key}
     */
    @RequestMapping(value = "/{bucket}/{key:.*}", method = RequestMethod.HEAD)
    public ResponseEntity<Void> headObject(
            @PathVariable String bucket,
            @PathVariable String key,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            S3ObjectInfo objectInfo = s3StyleFileService.getObjectInfo(bucket, key, username);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, objectInfo.getContentType())
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(objectInfo.getSize()))
                    .header("ETag", objectInfo.getEtag())
                    .header("Last-Modified", objectInfo.getLastModified().toString())
                    .build();
        } catch (Exception e) {
            log.error("获取对象元数据失败: bucket={}, key={}", bucket, key, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    /**
     * 获取对象详细信息（JSON格式）
     * GET /filetransfer/api/s3/info/{bucket}/{key}
     */
    @GetMapping("/info/{bucket}/{key:.*}")
    public ApiResult<S3ObjectInfo> getObjectInfo(
            @PathVariable String bucket,
            @PathVariable String key,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            S3ObjectInfo objectInfo = s3StyleFileService.getObjectInfo(bucket, key, username);
            return ApiResult.success(objectInfo);
        } catch (Exception e) {
            log.error("获取对象信息失败: bucket={}, key={}", bucket, key, e);
            return ApiResult.error(404, "对象不存在");
        }
    }

    /**
     * 列出存储桶中的对象
     * GET /filetransfer/api/s3/
     */
    @GetMapping("/")
    public ApiResult<S3ListObjectsResponse> listObjects(
            @RequestParam String bucket,
            @RequestParam(required = false) String prefix,
            @RequestParam(required = false) String delimiter,
            @RequestParam(defaultValue = "1000") int maxKeys,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            S3ListObjectsResponse response = s3StyleFileService.listObjects(bucket, prefix, delimiter, maxKeys, username);
            return ApiResult.success(response);
        } catch (Exception e) {
            log.error("列出对象失败: bucket={}", bucket, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 删除对象
     * DELETE /filetransfer/api/s3/{bucket}/{key}
     */
    @DeleteMapping("/{bucket}/{key:.*}")
    public ApiResult<String> deleteObject(
            @PathVariable String bucket,
            @PathVariable String key,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            s3StyleFileService.deleteObject(bucket, key, username);
            return ApiResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除对象失败: bucket={}, key={}", bucket, key, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 批量删除对象
     * POST /filetransfer/api/s3/delete
     */
    @PostMapping("/delete")
    public ApiResult<Map<String, Object>> deleteObjects(
            @RequestBody DeleteObjectsRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            Map<String, Object> result = s3StyleFileService.deleteObjects(request, username);
            return ApiResult.success(result);
        } catch (Exception e) {
            log.error("批量删除对象失败", e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 查询传输进度
     * GET /filetransfer/api/s3/progress/{transferId}
     */
    @GetMapping("/progress/{transferId}")
    public ApiResult<TransferProgressResponse> getProgress(
            @PathVariable String transferId,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            TransferProgressResponse progress = s3StyleFileService.getTransferProgress(transferId, username);
            return ApiResult.success(progress);
        } catch (Exception e) {
            log.error("查询传输进度失败: transferId={}", transferId, e);
            return ApiResult.error(404, "传输记录不存在");
        }
    }

    /**
     * 服务健康检查
     * GET /filetransfer/api/s3/health
     */
    @GetMapping("/health")
    public ApiResult<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "S3 File Transfer Service");
        health.put("timestamp", System.currentTimeMillis());
        return ApiResult.success(health);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
} 