package com.sdesrd.filetransfer.server.dto;

import java.util.List;

import lombok.Data;

/**
 * S3风格的列表对象响应DTO
 */
@Data
public class S3ListObjectsResponse {
    
    /**
     * 存储桶名称（对应用户名）
     */
    private String bucketName;
    
    /**
     * 前缀
     */
    private String prefix;
    
    /**
     * 分隔符
     */
    private String delimiter;
    
    /**
     * 最大键数量
     */
    private Integer maxKeys;
    
    /**
     * 是否截断
     */
    private Boolean isTruncated;
    
    /**
     * 下一个标记
     */
    private String nextMarker;
    
    /**
     * 对象列表
     */
    private List<S3ObjectInfo> contents;
    
    /**
     * 公共前缀列表（"目录"）
     */
    private List<String> commonPrefixes;
    
    /**
     * 实际返回的对象数量
     */
    private Integer keyCount;
    
    /**
     * 继续标记（用于分页）
     */
    private String continuationToken;
    
    /**
     * 下一个继续标记
     */
    private String nextContinuationToken;
} 