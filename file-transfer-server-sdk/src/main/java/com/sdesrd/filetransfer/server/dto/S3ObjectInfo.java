package com.sdesrd.filetransfer.server.dto;

import java.time.Instant;

import lombok.Data;

/**
 * S3风格的对象信息DTO
 */
@Data
public class S3ObjectInfo {
    
    /**
     * 对象键（相对路径）
     */
    private String key;
    
    /**
     * 最后修改时间
     */
    private Instant lastModified;
    
    /**
     * ETag值
     */
    private String etag;
    
    /**
     * 文件大小
     */
    private Long size;
    
    /**
     * 存储类别
     */
    private String storageClass;
    
    /**
     * 拥有者信息
     */
    private String owner;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 是否为最新版本
     */
    private Boolean isLatest;
    
    /**
     * 是否为目录（以分隔符结尾）
     */
    private Boolean isDirectory;
    
    /**
     * 格式化的文件大小
     */
    private String formattedSize;
    
    /**
     * 创建时间
     */
    private Instant createdTime;
} 