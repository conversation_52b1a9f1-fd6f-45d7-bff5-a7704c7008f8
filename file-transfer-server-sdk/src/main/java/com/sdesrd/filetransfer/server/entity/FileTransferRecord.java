package com.sdesrd.filetransfer.server.entity;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 文件传输记录实体
 * 用于记录文件传输的历史和统计信息
 */
@Data
public class FileTransferRecord {
    
    // === 常量定义 ===
    
    /**
     * ID前缀
     */
    private static final String ID_PREFIX = "ftr_";
    
    /**
     * 传输类型枚举
     */
    public enum TransferType {
        UPLOAD("上传"),
        DOWNLOAD("下载"),
        DELETE("删除");
        
        private final String description;
        
        TransferType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 传输状态枚举
     */
    public enum TransferStatus {
        STARTED("开始"),
        IN_PROGRESS("进行中"),
        COMPLETED("完成"),
        FAILED("失败"),
        CANCELLED("取消");
        
        private final String description;
        
        TransferStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // === 实体字段 ===
    
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 文件ID
     */
    private String fileId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 传输类型
     */
    private TransferType transferType;
    
    /**
     * 传输状态
     */
    private TransferStatus transferStatus;
    
    /**
     * 已传输字节数
     */
    private Long transferredBytes;
    
    /**
     * 传输速度（字节/秒）
     */
    private Long transferSpeed;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 客户端IP地址
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 额外信息（JSON格式）
     */
    private String extraInfo;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    // === 工具方法 ===
    
    /**
     * 生成唯一ID
     * 
     * @return 唯一ID
     */
    public static String generateId() {
        return ID_PREFIX + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
    }
    
    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (this.id == null) {
            this.id = generateId();
        }
        if (this.transferredBytes == null) {
            this.transferredBytes = 0L;
        }
        if (this.transferSpeed == null) {
            this.transferSpeed = 0L;
        }
        if (this.transferStatus == null) {
            this.transferStatus = TransferStatus.STARTED;
        }
        if (this.createTime == null) {
            this.createTime = LocalDateTime.now();
        }
        if (this.updateTime == null) {
            this.updateTime = LocalDateTime.now();
        }
    }
    
    /**
     * 更新时间戳
     */
    public void updateTimestamp() {
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 计算传输进度百分比
     * 
     * @return 进度百分比（0-100）
     */
    public double getProgressPercentage() {
        if (fileSize == null || fileSize <= 0 || transferredBytes == null) {
            return 0.0;
        }
        return Math.min(100.0, (transferredBytes.doubleValue() / fileSize.doubleValue()) * 100.0);
    }
    
    /**
     * 计算传输持续时间（毫秒）
     * 
     * @return 持续时间
     */
    public long getDurationMillis() {
        if (startTime == null) {
            return 0;
        }
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMillis();
    }
    
    /**
     * 获取格式化的传输速度
     * 
     * @return 格式化的传输速度字符串
     */
    public String getFormattedTransferSpeed() {
        if (transferSpeed == null || transferSpeed <= 0) {
            return "0 B/s";
        }
        
        String[] units = {"B/s", "KB/s", "MB/s", "GB/s"};
        int unitIndex = 0;
        double speed = transferSpeed.doubleValue();
        
        while (speed >= 1024 && unitIndex < units.length - 1) {
            speed /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", speed, units[unitIndex]);
    }
    
    /**
     * 获取格式化的文件大小
     * 
     * @return 格式化的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize <= 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
    
    /**
     * 检查传输是否完成
     * 
     * @return 是否完成
     */
    public boolean isCompleted() {
        return transferStatus == TransferStatus.COMPLETED;
    }
    
    /**
     * 检查传输是否失败
     * 
     * @return 是否失败
     */
    public boolean isFailed() {
        return transferStatus == TransferStatus.FAILED;
    }
    
    /**
     * 检查传输是否正在进行
     * 
     * @return 是否正在进行
     */
    public boolean isInProgress() {
        return transferStatus == TransferStatus.IN_PROGRESS || transferStatus == TransferStatus.STARTED;
    }
    
    @Override
    public String toString() {
        return String.format("FileTransferRecord{id='%s', username='%s', fileName='%s', transferType=%s, transferStatus=%s, progress=%.2f%%}",
                id, username, fileName, transferType, transferStatus, getProgressPercentage());
    }
}
