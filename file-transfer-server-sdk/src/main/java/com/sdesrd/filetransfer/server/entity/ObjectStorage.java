package com.sdesrd.filetransfer.server.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 对象存储实体
 */
@Data
@TableName("object_storage")
public class ObjectStorage {
    
    /**
     * 唯一ID
     */
    @TableId
    private String id;
    
    /**
     * 存储桶名称
     */
    private String bucket;
    
    /**
     * 对象key
     */
    private String objectKey;
    
    /**
     * 物理文件ID (MD5)
     */
    private String fileId;
    
    /**
     * 原始文件名
     */
    private String fileName;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 文件类型
     */
    private String contentType;
    
    /**
     * MD5哈希值
     */
    private String md5Hash;
    
    /**
     * 创建用户
     */
    private String userName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 生成唯一ID
     */
    public static String generateId() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }
} 