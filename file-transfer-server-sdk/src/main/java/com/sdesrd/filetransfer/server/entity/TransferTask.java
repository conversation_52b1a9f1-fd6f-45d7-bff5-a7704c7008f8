package com.sdesrd.filetransfer.server.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 传输任务实体
 */
@Data
@TableName("transfer_task")
public class TransferTask {
    
    /**
     * 传输ID
     */
    @TableId
    private String transferId;
    
    /**
     * 存储桶
     */
    private String bucket;
    
    /**
     * 对象key
     */
    private String objectKey;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 文件MD5
     */
    private String fileMd5;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 分块大小
     */
    private Integer chunkSize;
    
    /**
     * 总分块数
     */
    private Integer totalChunks;
    
    /**
     * 已完成分块数
     */
    private Integer completedChunks;
    
    /**
     * 状态：0-初始化，1-上传中，2-完成，3-失败
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 传输状态枚举
     */
    public static class Status {
        public static final int INITIALIZED = 0;
        public static final int UPLOADING = 1;
        public static final int COMPLETED = 2;
        public static final int FAILED = 3;
    }
    
    /**
     * 生成传输ID
     */
    public static String generateTransferId() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }
} 