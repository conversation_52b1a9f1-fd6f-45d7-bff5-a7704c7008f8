package com.sdesrd.filetransfer.server.entity;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 用户文件映射实体
 * 用于S3风格API的用户文件关联
 */
@Data
public class UserFileMapping {
    
    // === 常量定义 ===
    
    /**
     * ID前缀
     */
    private static final String ID_PREFIX = "ufm_";
    
    // === 实体字段 ===
    
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 文件key（S3风格的对象key）
     */
    private String fileKey;
    
    /**
     * 文件ID（关联到物理文件）
     */
    private String fileId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * ETag（通常是MD5值）
     */
    private String etag;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 是否为最新版本
     */
    private Boolean isLatest;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    // === 工具方法 ===
    
    /**
     * 生成唯一ID
     * 
     * @return 唯一ID
     */
    public static String generateId() {
        return ID_PREFIX + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
    }
    
    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (this.id == null) {
            this.id = generateId();
        }
        if (this.version == null) {
            this.version = 1;
        }
        if (this.isLatest == null) {
            this.isLatest = true;
        }
        if (this.createTime == null) {
            this.createTime = LocalDateTime.now();
        }
        if (this.updateTime == null) {
            this.updateTime = LocalDateTime.now();
        }
    }
    
    /**
     * 更新时间戳
     */
    public void updateTimestamp() {
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 检查是否为有效的映射
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return username != null && !username.trim().isEmpty()
            && fileKey != null && !fileKey.trim().isEmpty()
            && fileId != null && !fileId.trim().isEmpty()
            && fileName != null && !fileName.trim().isEmpty();
    }
    
    /**
     * 获取格式化的文件大小
     * 
     * @return 格式化的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize <= 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
    
    /**
     * 复制基本信息到新的映射对象
     * 
     * @return 新的映射对象
     */
    public UserFileMapping copyBasicInfo() {
        UserFileMapping copy = new UserFileMapping();
        copy.setUsername(this.username);
        copy.setFileKey(this.fileKey);
        copy.setFileId(this.fileId);
        copy.setFileName(this.fileName);
        copy.setFileSize(this.fileSize);
        copy.setContentType(this.contentType);
        copy.setEtag(this.etag);
        return copy;
    }
    
    @Override
    public String toString() {
        return String.format("UserFileMapping{id='%s', username='%s', fileKey='%s', fileName='%s', fileSize=%d, version=%d, isLatest=%s}",
                id, username, fileKey, fileName, fileSize, version, isLatest);
    }
}
