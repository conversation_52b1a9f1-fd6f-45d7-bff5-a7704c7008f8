package com.sdesrd.filetransfer.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdesrd.filetransfer.server.entity.ChunkRecord;

/**
 * 分块记录Mapper
 */
@Mapper
public interface ChunkRecordMapper extends BaseMapper<ChunkRecord> {
    
    /**
     * 根据传输ID查询所有分块
     */
    @Select("SELECT * FROM chunk_record WHERE transfer_id = #{transferId} ORDER BY chunk_index")
    List<ChunkRecord> findByTransferId(@Param("transferId") String transferId);
    
    /**
     * 查询特定分块
     */
    @Select("SELECT * FROM chunk_record WHERE transfer_id = #{transferId} AND chunk_index = #{chunkIndex}")
    ChunkRecord findByTransferIdAndIndex(@Param("transferId") String transferId, @Param("chunkIndex") Integer chunkIndex);
    
    /**
     * 更新分块状态
     */
    @Update("UPDATE chunk_record SET status = #{status} WHERE transfer_id = #{transferId} AND chunk_index = #{chunkIndex}")
    int updateChunkStatus(@Param("transferId") String transferId, 
                         @Param("chunkIndex") Integer chunkIndex, 
                         @Param("status") Integer status);
    
    /**
     * 统计已完成的分块数
     */
    @Select("SELECT COUNT(*) FROM chunk_record WHERE transfer_id = #{transferId} AND status = 1")
    int countCompletedChunks(@Param("transferId") String transferId);
    
    /**
     * 删除传输任务的所有分块记录
     */
    @Update("DELETE FROM chunk_record WHERE transfer_id = #{transferId}")
    int deleteByTransferId(@Param("transferId") String transferId);
} 