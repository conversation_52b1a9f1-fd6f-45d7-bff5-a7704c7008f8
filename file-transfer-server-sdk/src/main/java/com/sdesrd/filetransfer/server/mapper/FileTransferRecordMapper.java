package com.sdesrd.filetransfer.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.sdesrd.filetransfer.server.entity.FileTransferRecord;

/**
 * 文件传输记录数据访问接口
 * 用于记录和查询文件传输历史
 */
@Mapper
public interface FileTransferRecordMapper {
    
    /**
     * 插入传输记录
     * 
     * @param record 传输记录
     * @return 影响行数
     */
    @Insert("INSERT INTO file_transfer_record (id, username, file_id, file_name, file_size, " +
            "transfer_type, transfer_status, transferred_bytes, transfer_speed, " +
            "start_time, end_time, error_message, client_ip, user_agent, extra_info, " +
            "create_time, update_time) " +
            "VALUES (#{id}, #{username}, #{fileId}, #{fileName}, #{fileSize}, " +
            "#{transferType}, #{transferStatus}, #{transferredBytes}, #{transferSpeed}, " +
            "#{startTime}, #{endTime}, #{errorMessage}, #{clientIp}, #{userAgent}, #{extraInfo}, " +
            "#{createTime}, #{updateTime})")
    int insert(FileTransferRecord record);
    
    /**
     * 根据ID查找传输记录
     * 
     * @param id 记录ID
     * @return 传输记录
     */
    @Select("SELECT * FROM file_transfer_record WHERE id = #{id}")
    FileTransferRecord findById(@Param("id") String id);
    
    /**
     * 根据用户名查找传输记录
     * 
     * @param username 用户名
     * @return 传输记录列表
     */
    @Select("SELECT * FROM file_transfer_record WHERE username = #{username} ORDER BY create_time DESC")
    List<FileTransferRecord> findByUsername(@Param("username") String username);
    
    /**
     * 根据用户名和传输类型查找传输记录
     * 
     * @param username 用户名
     * @param transferType 传输类型
     * @return 传输记录列表
     */
    @Select("SELECT * FROM file_transfer_record WHERE username = #{username} AND transfer_type = #{transferType} ORDER BY create_time DESC")
    List<FileTransferRecord> findByUsernameAndType(@Param("username") String username, @Param("transferType") String transferType);
    
    /**
     * 根据用户名和传输状态查找传输记录
     * 
     * @param username 用户名
     * @param transferStatus 传输状态
     * @return 传输记录列表
     */
    @Select("SELECT * FROM file_transfer_record WHERE username = #{username} AND transfer_status = #{transferStatus} ORDER BY create_time DESC")
    List<FileTransferRecord> findByUsernameAndStatus(@Param("username") String username, @Param("transferStatus") String transferStatus);
    
    /**
     * 根据文件ID查找传输记录
     * 
     * @param fileId 文件ID
     * @return 传输记录列表
     */
    @Select("SELECT * FROM file_transfer_record WHERE file_id = #{fileId} ORDER BY create_time DESC")
    List<FileTransferRecord> findByFileId(@Param("fileId") String fileId);
    
    /**
     * 更新传输记录
     * 
     * @param record 传输记录
     * @return 影响行数
     */
    @Update("UPDATE file_transfer_record SET transfer_status = #{transferStatus}, " +
            "transferred_bytes = #{transferredBytes}, transfer_speed = #{transferSpeed}, " +
            "end_time = #{endTime}, error_message = #{errorMessage}, " +
            "extra_info = #{extraInfo}, update_time = #{updateTime} WHERE id = #{id}")
    int update(FileTransferRecord record);
    
    /**
     * 更新传输状态
     * 
     * @param id 记录ID
     * @param transferStatus 传输状态
     * @return 影响行数
     */
    @Update("UPDATE file_transfer_record SET transfer_status = #{transferStatus}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("transferStatus") String transferStatus);
    
    /**
     * 更新传输进度
     * 
     * @param id 记录ID
     * @param transferredBytes 已传输字节数
     * @param transferSpeed 传输速度
     * @return 影响行数
     */
    @Update("UPDATE file_transfer_record SET transferred_bytes = #{transferredBytes}, " +
            "transfer_speed = #{transferSpeed}, update_time = NOW() WHERE id = #{id}")
    int updateProgress(@Param("id") String id, @Param("transferredBytes") Long transferredBytes, @Param("transferSpeed") Long transferSpeed);
    
    /**
     * 根据用户名统计传输记录数量
     * 
     * @param username 用户名
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM file_transfer_record WHERE username = #{username}")
    int countByUsername(@Param("username") String username);
    
    /**
     * 根据用户名和传输类型统计记录数量
     * 
     * @param username 用户名
     * @param transferType 传输类型
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM file_transfer_record WHERE username = #{username} AND transfer_type = #{transferType}")
    int countByUsernameAndType(@Param("username") String username, @Param("transferType") String transferType);
    
    /**
     * 根据用户名计算总传输字节数
     * 
     * @param username 用户名
     * @return 总传输字节数
     */
    @Select("SELECT COALESCE(SUM(transferred_bytes), 0) FROM file_transfer_record WHERE username = #{username}")
    long sumTransferredBytesByUsername(@Param("username") String username);
    
    /**
     * 查找指定时间范围内的传输记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 传输记录列表
     */
    @Select("SELECT * FROM file_transfer_record WHERE create_time BETWEEN #{startTime} AND #{endTime} ORDER BY create_time DESC")
    List<FileTransferRecord> findByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
    
    /**
     * 查找正在进行的传输记录
     * 
     * @return 传输记录列表
     */
    @Select("SELECT * FROM file_transfer_record WHERE transfer_status IN ('STARTED', 'IN_PROGRESS') ORDER BY create_time DESC")
    List<FileTransferRecord> findActiveTransfers();
    
    /**
     * 查找失败的传输记录
     * 
     * @return 传输记录列表
     */
    @Select("SELECT * FROM file_transfer_record WHERE transfer_status = 'FAILED' ORDER BY create_time DESC")
    List<FileTransferRecord> findFailedTransfers();
    
    /**
     * 删除指定时间之前的传输记录
     * 
     * @param beforeTime 时间阈值
     * @return 影响行数
     */
    @Delete("DELETE FROM file_transfer_record WHERE create_time < #{beforeTime}")
    int deleteBeforeTime(@Param("beforeTime") String beforeTime);
    
    /**
     * 根据用户名删除传输记录
     * 
     * @param username 用户名
     * @return 影响行数
     */
    @Delete("DELETE FROM file_transfer_record WHERE username = #{username}")
    int deleteByUsername(@Param("username") String username);
    
    /**
     * 根据文件ID删除传输记录
     * 
     * @param fileId 文件ID
     * @return 影响行数
     */
    @Delete("DELETE FROM file_transfer_record WHERE file_id = #{fileId}")
    int deleteByFileId(@Param("fileId") String fileId);
    
    /**
     * 批量插入传输记录
     * 
     * @param records 传输记录列表
     * @return 影响行数
     */
    @Insert("<script>" +
            "INSERT INTO file_transfer_record (id, username, file_id, file_name, file_size, " +
            "transfer_type, transfer_status, transferred_bytes, transfer_speed, " +
            "start_time, end_time, error_message, client_ip, user_agent, extra_info, " +
            "create_time, update_time) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.username}, #{item.fileId}, #{item.fileName}, #{item.fileSize}, " +
            "#{item.transferType}, #{item.transferStatus}, #{item.transferredBytes}, #{item.transferSpeed}, " +
            "#{item.startTime}, #{item.endTime}, #{item.errorMessage}, #{item.clientIp}, #{item.userAgent}, #{item.extraInfo}, " +
            "#{item.createTime}, #{item.updateTime})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("list") List<FileTransferRecord> records);
    
    /**
     * 获取传输统计信息
     * 
     * @param username 用户名（可选）
     * @return 统计信息Map
     */
    @Select("<script>" +
            "SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN transfer_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_count, " +
            "SUM(CASE WHEN transfer_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count, " +
            "SUM(CASE WHEN transfer_status IN ('STARTED', 'IN_PROGRESS') THEN 1 ELSE 0 END) as active_count, " +
            "COALESCE(SUM(transferred_bytes), 0) as total_bytes " +
            "FROM file_transfer_record " +
            "<if test='username != null'>" +
            "WHERE username = #{username}" +
            "</if>" +
            "</script>")
    java.util.Map<String, Object> getTransferStatistics(@Param("username") String username);
}
