package com.sdesrd.filetransfer.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdesrd.filetransfer.server.entity.TransferTask;

/**
 * 传输任务Mapper
 */
@Mapper
public interface TransferTaskMapper extends BaseMapper<TransferTask> {
    
    /**
     * 更新传输进度
     */
    @Update("UPDATE transfer_task SET completed_chunks = #{completedChunks}, status = #{status}, update_time = datetime('now') WHERE transfer_id = #{transferId}")
    int updateProgress(@Param("transferId") String transferId, 
                      @Param("completedChunks") Integer completedChunks, 
                      @Param("status") Integer status);
    
    /**
     * 完成传输任务
     */
    @Update("UPDATE transfer_task SET status = #{status}, complete_time = datetime('now'), update_time = datetime('now') WHERE transfer_id = #{transferId}")
    int completeTransfer(@Param("transferId") String transferId, @Param("status") Integer status);
    
    /**
     * 根据状态查询传输任务
     */
    @Select("SELECT * FROM transfer_task WHERE status = #{status}")
    List<TransferTask> findByStatus(@Param("status") Integer status);
    
    /**
     * 查询用户的传输任务
     */
    @Select("SELECT * FROM transfer_task WHERE user_name = #{userName} ORDER BY create_time DESC")
    List<TransferTask> findByUser(@Param("userName") String userName);
    
    /**
     * 清理过期的传输任务
     */
    @Update("DELETE FROM transfer_task WHERE status IN (2, 3) AND create_time < datetime('now', '-24 hours')")
    int cleanupExpiredTasks();
} 