package com.sdesrd.filetransfer.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.sdesrd.filetransfer.server.entity.UserFileMapping;

/**
 * 用户文件映射数据访问接口
 * 用于S3风格API的用户文件关联操作
 */
@Mapper
public interface UserFileMappingMapper {
    
    /**
     * 插入用户文件映射
     * 
     * @param mapping 用户文件映射
     * @return 影响行数
     */
    @Insert("INSERT INTO user_file_mapping (id, username, file_key, file_id, file_name, file_size, " +
            "content_type, etag, version, is_latest, create_time, update_time) " +
            "VALUES (#{id}, #{username}, #{fileKey}, #{fileId}, #{fileName}, #{fileSize}, " +
            "#{contentType}, #{etag}, #{version}, #{isLatest}, #{createTime}, #{updateTime})")
    int insert(UserFileMapping mapping);
    
    /**
     * 根据用户名和文件key查找映射
     * 
     * @param username 用户名
     * @param fileKey 文件key
     * @return 用户文件映射
     */
    @Select("SELECT * FROM user_file_mapping WHERE username = #{username} AND file_key = #{fileKey} AND is_latest = 1")
    UserFileMapping findByUserAndKey(@Param("username") String username, @Param("fileKey") String fileKey);
    
    /**
     * 根据用户名查找所有映射
     * 
     * @param username 用户名
     * @return 用户文件映射列表
     */
    @Select("SELECT * FROM user_file_mapping WHERE username = #{username} AND is_latest = 1 ORDER BY create_time DESC")
    List<UserFileMapping> findByUser(@Param("username") String username);
    
    /**
     * 根据用户名和前缀查找映射
     * 
     * @param username 用户名
     * @param prefix 文件key前缀
     * @return 用户文件映射列表
     */
    @Select("SELECT * FROM user_file_mapping WHERE username = #{username} AND file_key LIKE CONCAT(#{prefix}, '%') AND is_latest = 1 ORDER BY file_key")
    List<UserFileMapping> findByUserAndPrefix(@Param("username") String username, @Param("prefix") String prefix);
    
    /**
     * 根据用户名和文件key删除映射
     * 
     * @param username 用户名
     * @param fileKey 文件key
     * @return 影响行数
     */
    @Delete("DELETE FROM user_file_mapping WHERE username = #{username} AND file_key = #{fileKey}")
    int deleteByUserAndKey(@Param("username") String username, @Param("fileKey") String fileKey);
    
    /**
     * 统计文件引用次数
     * 
     * @param fileId 文件ID
     * @return 引用次数
     */
    @Select("SELECT COUNT(*) FROM user_file_mapping WHERE file_id = #{fileId} AND is_latest = 1")
    int countFileReferences(@Param("fileId") String fileId);
    
    /**
     * 更新映射为非最新版本
     * 
     * @param username 用户名
     * @param fileKey 文件key
     * @return 影响行数
     */
    @Update("UPDATE user_file_mapping SET is_latest = 0, update_time = NOW() WHERE username = #{username} AND file_key = #{fileKey}")
    int markAsNotLatest(@Param("username") String username, @Param("fileKey") String fileKey);
    
    /**
     * 根据文件ID查找所有映射
     * 
     * @param fileId 文件ID
     * @return 用户文件映射列表
     */
    @Select("SELECT * FROM user_file_mapping WHERE file_id = #{fileId}")
    List<UserFileMapping> findByFileId(@Param("fileId") String fileId);
    
    /**
     * 根据ID查找映射
     * 
     * @param id 映射ID
     * @return 用户文件映射
     */
    @Select("SELECT * FROM user_file_mapping WHERE id = #{id}")
    UserFileMapping findById(@Param("id") String id);
    
    /**
     * 更新映射
     * 
     * @param mapping 用户文件映射
     * @return 影响行数
     */
    @Update("UPDATE user_file_mapping SET file_name = #{fileName}, file_size = #{fileSize}, " +
            "content_type = #{contentType}, etag = #{etag}, version = #{version}, " +
            "is_latest = #{isLatest}, update_time = #{updateTime} WHERE id = #{id}")
    int update(UserFileMapping mapping);
    
    /**
     * 根据用户名统计文件数量
     * 
     * @param username 用户名
     * @return 文件数量
     */
    @Select("SELECT COUNT(*) FROM user_file_mapping WHERE username = #{username} AND is_latest = 1")
    int countByUser(@Param("username") String username);
    
    /**
     * 根据用户名计算总文件大小
     * 
     * @param username 用户名
     * @return 总文件大小
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM user_file_mapping WHERE username = #{username} AND is_latest = 1")
    long sumFileSizeByUser(@Param("username") String username);
    
    /**
     * 查找指定时间之前的映射
     * 
     * @param beforeTime 时间阈值
     * @return 用户文件映射列表
     */
    @Select("SELECT * FROM user_file_mapping WHERE update_time < #{beforeTime}")
    List<UserFileMapping> findBeforeTime(@Param("beforeTime") String beforeTime);
    
    /**
     * 删除指定时间之前的映射
     * 
     * @param beforeTime 时间阈值
     * @return 影响行数
     */
    @Delete("DELETE FROM user_file_mapping WHERE update_time < #{beforeTime}")
    int deleteBeforeTime(@Param("beforeTime") String beforeTime);
    
    /**
     * 根据用户名和文件key模式查找映射
     * 
     * @param username 用户名
     * @param pattern 文件key模式（支持SQL LIKE语法）
     * @return 用户文件映射列表
     */
    @Select("SELECT * FROM user_file_mapping WHERE username = #{username} AND file_key LIKE #{pattern} AND is_latest = 1 ORDER BY file_key")
    List<UserFileMapping> findByUserAndPattern(@Param("username") String username, @Param("pattern") String pattern);
    
    /**
     * 批量插入用户文件映射
     * 
     * @param mappings 用户文件映射列表
     * @return 影响行数
     */
    @Insert("<script>" +
            "INSERT INTO user_file_mapping (id, username, file_key, file_id, file_name, file_size, " +
            "content_type, etag, version, is_latest, create_time, update_time) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.username}, #{item.fileKey}, #{item.fileId}, #{item.fileName}, #{item.fileSize}, " +
            "#{item.contentType}, #{item.etag}, #{item.version}, #{item.isLatest}, #{item.createTime}, #{item.updateTime})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("list") List<UserFileMapping> mappings);
    
    /**
     * 根据用户名和文件key列表批量删除映射
     * 
     * @param username 用户名
     * @param fileKeys 文件key列表
     * @return 影响行数
     */
    @Delete("<script>" +
            "DELETE FROM user_file_mapping WHERE username = #{username} AND file_key IN " +
            "<foreach collection='fileKeys' item='key' open='(' separator=',' close=')'>" +
            "#{key}" +
            "</foreach>" +
            "</script>")
    int batchDeleteByUserAndKeys(@Param("username") String username, @Param("fileKeys") List<String> fileKeys);
}
