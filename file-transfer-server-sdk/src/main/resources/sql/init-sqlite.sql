-- 文件传输系统数据库初始化脚本
-- 版本: 4.0.0 (S3-only simplified version)
-- 创建时间: 2024-01-15

-- 系统版本表
CREATE TABLE IF NOT EXISTS system_version (
    id INTEGER PRIMARY KEY,
    version TEXT NOT NULL,
    update_time TEXT NOT NULL
);

-- 插入或更新版本信息
INSERT OR REPLACE INTO system_version (id, version, update_time) 
VALUES (1, '4.0.0', datetime('now'));

-- 对象存储映射表（简化设计）
CREATE TABLE IF NOT EXISTS object_storage (
    id TEXT PRIMARY KEY,              -- 唯一ID
    bucket TEXT NOT NULL,             -- 存储桶名称
    object_key TEXT NOT NULL,         -- 对象key
    file_id TEXT NOT NULL,            -- 物理文件ID (MD5)
    file_name TEXT NOT NULL,          -- 原始文件名
    file_size INTEGER NOT NULL,       -- 文件大小
    content_type TEXT,                -- 文件类型
    md5_hash TEXT NOT NULL,           -- MD5哈希值
    user_name TEXT NOT NULL,          -- 创建用户
    create_time TEXT NOT NULL,        -- 创建时间
    update_time TEXT NOT NULL         -- 更新时间
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_object_bucket_key ON object_storage(bucket, object_key);
CREATE INDEX IF NOT EXISTS idx_object_user ON object_storage(user_name);
CREATE INDEX IF NOT EXISTS idx_object_file_id ON object_storage(file_id);
CREATE INDEX IF NOT EXISTS idx_object_md5 ON object_storage(md5_hash);

-- 确保同一存储桶中的key唯一
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_bucket_key ON object_storage(bucket, object_key);

-- 传输任务表（用于跟踪上传进度）
CREATE TABLE IF NOT EXISTS transfer_task (
    transfer_id TEXT PRIMARY KEY,     -- 传输ID
    bucket TEXT NOT NULL,             -- 存储桶
    object_key TEXT NOT NULL,         -- 对象key
    file_name TEXT NOT NULL,          -- 文件名
    file_size INTEGER NOT NULL,       -- 文件大小
    file_md5 TEXT NOT NULL,           -- 文件MD5
    user_name TEXT NOT NULL,          -- 用户名
    chunk_size INTEGER NOT NULL,      -- 分块大小
    total_chunks INTEGER NOT NULL,    -- 总分块数
    completed_chunks INTEGER DEFAULT 0, -- 已完成分块数
    status INTEGER DEFAULT 0,         -- 状态：0-初始化，1-上传中，2-完成，3-失败
    create_time TEXT NOT NULL,        -- 创建时间
    update_time TEXT NOT NULL,        -- 更新时间
    complete_time TEXT                -- 完成时间
);

-- 创建传输任务索引
CREATE INDEX IF NOT EXISTS idx_transfer_user ON transfer_task(user_name);
CREATE INDEX IF NOT EXISTS idx_transfer_status ON transfer_task(status);
CREATE INDEX IF NOT EXISTS idx_transfer_bucket_key ON transfer_task(bucket, object_key);

-- 分块记录表
CREATE TABLE IF NOT EXISTS chunk_record (
    id TEXT PRIMARY KEY,              -- 唯一ID
    transfer_id TEXT NOT NULL,        -- 传输ID
    chunk_index INTEGER NOT NULL,     -- 分块索引
    chunk_size INTEGER NOT NULL,      -- 分块大小
    chunk_md5 TEXT NOT NULL,          -- 分块MD5
    chunk_path TEXT NOT NULL,         -- 分块临时路径
    status INTEGER DEFAULT 0,         -- 状态：0-待上传，1-已上传
    create_time TEXT NOT NULL,        -- 创建时间
    FOREIGN KEY (transfer_id) REFERENCES transfer_task(transfer_id)
);

-- 创建分块索引
CREATE INDEX IF NOT EXISTS idx_chunk_transfer ON chunk_record(transfer_id);
CREATE INDEX IF NOT EXISTS idx_chunk_status ON chunk_record(status);
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_transfer_chunk ON chunk_record(transfer_id, chunk_index); 