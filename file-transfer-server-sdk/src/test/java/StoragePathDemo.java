import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 存储路径逻辑演示程序
 * 独立测试新的存储路径逻辑：${bucket.storage-path}/{key}
 */
public class StoragePathDemo {
    
    // === 常量定义 ===
    
    /**
     * 路径分隔符
     */
    private static final String PATH_SEPARATOR = "/";
    
    /**
     * 危险字符列表
     */
    private static final String[] DANGEROUS_CHARS = {"<", ">", ":", "\"", "|", "?", "*"};
    
    /**
     * 文件ID最小长度
     */
    private static final int MIN_FILE_ID_LENGTH = 4;
    
    public static void main(String[] args) {
        System.out.println("=== 存储路径逻辑演示程序 ===\n");
        
        try {
            // 创建临时测试目录
            Path tempDir = Files.createTempDirectory("storage-path-test");
            String bucketStoragePath = tempDir.resolve("buckets").resolve("test-bucket").toString();
            Files.createDirectories(Paths.get(bucketStoragePath));
            
            System.out.println("测试环境初始化完成");
            System.out.println("存储桶路径: " + bucketStoragePath);
            System.out.println();
            
            // 测试1：新存储路径格式验证
            testNewStoragePathFormat(bucketStoragePath);
            
            // 测试2：路径格式对比
            testPathFormatComparison(bucketStoragePath);
            
            // 测试3：路径安全性验证
            testPathSecurity();
            
            // 测试4：文件创建和访问
            testFileCreationAndAccess(bucketStoragePath);
            
            // 测试5：中文路径支持
            testChinesePathSupport(bucketStoragePath);
            
            System.out.println("\n=== 所有测试完成 ===");
            System.out.println("✅ 新的存储路径逻辑工作正常！");
            
            // 清理临时目录
            deleteDirectory(tempDir.toFile());
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试新存储路径格式
     */
    private static void testNewStoragePathFormat(String bucketStoragePath) {
        System.out.println("=== 测试1：新存储路径格式验证 ===");
        
        String[] testKeys = {
            "simple-file.txt",
            "reports/2024/annual.pdf",
            "images/photos/vacation.jpg",
            "data/users/user123/profile.json"
        };
        
        for (String key : testKeys) {
            // 新的存储路径格式：${bucket.storage-path}/{key}
            String newPath = buildNewStoragePath(bucketStoragePath, key);
            String expectedPath = bucketStoragePath + PATH_SEPARATOR + key;
            
            if (expectedPath.equals(newPath)) {
                System.out.println("✓ key: " + key);
                System.out.println("  路径: " + newPath);
            } else {
                System.out.println("❌ key: " + key);
                System.out.println("  预期: " + expectedPath);
                System.out.println("  实际: " + newPath);
            }
        }
        System.out.println();
    }
    
    /**
     * 测试路径格式对比
     */
    private static void testPathFormatComparison(String bucketStoragePath) {
        System.out.println("=== 测试2：路径格式对比（新 vs 旧） ===");
        
        String fileId = "abcd1234567890ef";
        String fileName = "test-document.pdf";
        String key = "reports/2024/annual-report.pdf";
        
        // 旧的存储路径格式：${storage-path}/${fileId前4位}/${fileId}/${fileName}
        String oldPath = buildOldStoragePath(bucketStoragePath, fileId, fileName);
        
        // 新的存储路径格式：${bucket.storage-path}/{key}
        String newPath = buildNewStoragePath(bucketStoragePath, key);
        
        System.out.println("旧路径格式: " + oldPath);
        System.out.println("新路径格式: " + newPath);
        
        // 计算路径层级
        int oldLevels = oldPath.split(PATH_SEPARATOR).length - bucketStoragePath.split(PATH_SEPARATOR).length;
        int newLevels = newPath.split(PATH_SEPARATOR).length - bucketStoragePath.split(PATH_SEPARATOR).length;
        
        System.out.println("旧路径层级: " + oldLevels);
        System.out.println("新路径层级: " + newLevels);
        
        // 验证新路径的优势
        boolean containsKey = newPath.contains(key);
        boolean notContainsFileId = !newPath.contains(fileId);
        boolean readable = newPath.contains("reports/2024");
        
        System.out.println("包含完整key: " + (containsKey ? "✓" : "❌"));
        System.out.println("不包含fileId: " + (notContainsFileId ? "✓" : "❌"));
        System.out.println("保持可读性: " + (readable ? "✓" : "❌"));
        System.out.println();
    }
    
    /**
     * 测试路径安全性
     */
    private static void testPathSecurity() {
        System.out.println("=== 测试3：路径安全性验证 ===");
        
        // 测试危险路径
        String[] dangerousKeys = {
            "../../../etc/passwd",      // 路径遍历
            "/etc/passwd",              // 绝对路径
            "C:\\Windows\\System32",    // Windows驱动器路径
            "file<name>.txt",           // 危险字符
            "file>name.txt",
            "file:name.txt"
        };
        
        for (String dangerousKey : dangerousKeys) {
            try {
                validateKeyForPath(dangerousKey);
                System.out.println("❌ 应该拒绝但未拒绝: " + dangerousKey);
            } catch (IllegalArgumentException e) {
                System.out.println("✓ 正确拒绝: " + dangerousKey + " (" + e.getMessage() + ")");
            }
        }
        System.out.println();
    }
    
    /**
     * 测试文件创建和访问
     */
    private static void testFileCreationAndAccess(String bucketStoragePath) {
        System.out.println("=== 测试4：文件创建和访问 ===");
        
        String[] testKeys = {
            "simple-file.txt",
            "reports/2024/annual.pdf",
            "images/photos/vacation.jpg"
        };
        
        String testContent = "这是测试文件内容，用于验证新的存储路径逻辑。";
        
        for (String key : testKeys) {
            try {
                // 构建文件路径
                String filePath = buildNewStoragePath(bucketStoragePath, key);
                File file = new File(filePath);
                
                // 创建父目录
                File parentDir = file.getParentFile();
                if (!parentDir.exists()) {
                    parentDir.mkdirs();
                }
                
                // 创建测试文件
                Files.write(file.toPath(), testContent.getBytes("UTF-8"));
                
                // 验证文件存在
                if (file.exists()) {
                    // 验证文件内容
                    String actualContent = new String(Files.readAllBytes(file.toPath()), "UTF-8");
                    if (testContent.equals(actualContent)) {
                        System.out.println("✓ key: " + key + " (大小: " + file.length() + " bytes)");
                    } else {
                        System.out.println("❌ 内容不匹配: " + key);
                    }
                } else {
                    System.out.println("❌ 文件不存在: " + key);
                }
                
            } catch (Exception e) {
                System.out.println("❌ 异常: " + key + " - " + e.getMessage());
            }
        }
        System.out.println();
    }
    
    /**
     * 测试中文路径支持
     */
    private static void testChinesePathSupport(String bucketStoragePath) {
        System.out.println("=== 测试5：中文路径支持 ===");
        
        String[] chineseKeys = {
            "文档/报告/年度总结.pdf",
            "图片/照片/风景照.jpg",
            "数据/用户/张三/个人资料.json"
        };
        
        String testContent = "中文内容测试";
        
        for (String key : chineseKeys) {
            try {
                String filePath = buildNewStoragePath(bucketStoragePath, key);
                File file = new File(filePath);
                
                // 创建父目录
                File parentDir = file.getParentFile();
                if (!parentDir.exists()) {
                    parentDir.mkdirs();
                }
                
                // 创建文件
                Files.write(file.toPath(), testContent.getBytes("UTF-8"));
                
                if (file.exists()) {
                    String actualContent = new String(Files.readAllBytes(file.toPath()), "UTF-8");
                    if (testContent.equals(actualContent)) {
                        System.out.println("✓ 中文key: " + key);
                    } else {
                        System.out.println("❌ 中文内容不匹配: " + key);
                    }
                } else {
                    System.out.println("❌ 中文文件不存在: " + key);
                }
                
            } catch (Exception e) {
                System.out.println("❌ 中文路径异常: " + key + " - " + e.getMessage());
            }
        }
        System.out.println();
    }
    
    // === 辅助方法 ===
    
    /**
     * 构建新的存储路径
     */
    private static String buildNewStoragePath(String bucketPath, String key) {
        validateKeyForPath(key);
        return bucketPath + PATH_SEPARATOR + key;
    }
    
    /**
     * 构建旧的存储路径（用于对比）
     */
    private static String buildOldStoragePath(String storagePath, String fileId, String fileName) {
        return storagePath + PATH_SEPARATOR + 
               fileId.substring(0, Math.min(MIN_FILE_ID_LENGTH, fileId.length())) + PATH_SEPARATOR + 
               fileId + PATH_SEPARATOR + 
               fileName;
    }
    
    /**
     * 验证key用于路径构建的安全性
     */
    private static void validateKeyForPath(String key) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("对象key不能为空");
        }
        
        // 检查路径遍历攻击
        if (key.contains("..")) {
            throw new IllegalArgumentException("对象key不能包含..路径");
        }
        
        // 检查绝对路径
        if (key.startsWith("/") || key.startsWith("\\")) {
            throw new IllegalArgumentException("对象key不能以路径分隔符开头");
        }
        
        // 检查Windows驱动器路径
        if (key.matches("^[A-Za-z]:.*")) {
            throw new IllegalArgumentException("对象key不能包含驱动器路径");
        }
        
        // 检查其他危险字符
        for (String dangerousChar : DANGEROUS_CHARS) {
            if (key.contains(dangerousChar)) {
                throw new IllegalArgumentException("对象key不能包含危险字符: " + dangerousChar);
            }
        }
    }
    
    /**
     * 递归删除目录
     */
    private static void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
