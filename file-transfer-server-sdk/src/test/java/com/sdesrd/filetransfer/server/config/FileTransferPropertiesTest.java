package com.sdesrd.filetransfer.server.config;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 文件传输配置属性测试
 * 验证配置的完整性和有效性
 */
@DisplayName("文件传输配置属性测试")
class FileTransferPropertiesTest {
    
    // === 常量定义 ===
    
    /**
     * 测试存储桶名称
     */
    private static final String TEST_BUCKET = "test-bucket";
    
    /**
     * 测试用户名
     */
    private static final String TEST_USER = "test-user";
    
    /**
     * 测试密钥
     */
    private static final String TEST_SECRET_KEY = "test-secret-key-2024";
    
    /**
     * 测试存储路径
     */
    private static final String TEST_STORAGE_PATH = "./data/test-bucket";
    
    // === 测试组件 ===
    
    private FileTransferProperties properties;
    
    @BeforeEach
    void setUp() {
        properties = new FileTransferProperties();
    }
    
    @Test
    @DisplayName("有效配置验证测试")
    void testValidConfiguration() {
        // 创建有效的存储桶配置
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName(TEST_BUCKET);
        bucketConfig.setStoragePath(TEST_STORAGE_PATH);
        bucketConfig.setMaxFileSize(104857600L); // 100MB
        bucketConfig.setDefaultChunkSize(2097152); // 2MB
        
        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put(TEST_BUCKET, bucketConfig);
        properties.setBuckets(buckets);
        
        // 创建有效的用户配置
        FileTransferProperties.UserConfig userConfig = new FileTransferProperties.UserConfig();
        userConfig.setSecretKey(TEST_SECRET_KEY);
        userConfig.setAllowedBuckets(new String[]{TEST_BUCKET});
        
        Map<String, FileTransferProperties.UserConfig> users = new HashMap<>();
        users.put(TEST_USER, userConfig);
        properties.setUsers(users);
        
        // 验证配置应该通过
        assertDoesNotThrow(() -> properties.validateConfiguration());
    }
    
    @Test
    @DisplayName("空存储桶配置异常测试")
    void testEmptyBucketsException() {
        // 创建用户配置但没有存储桶配置
        FileTransferProperties.UserConfig userConfig = new FileTransferProperties.UserConfig();
        userConfig.setSecretKey(TEST_SECRET_KEY);
        userConfig.setAllowedBuckets(new String[]{TEST_BUCKET});
        
        Map<String, FileTransferProperties.UserConfig> users = new HashMap<>();
        users.put(TEST_USER, userConfig);
        properties.setUsers(users);
        
        // 验证应该抛出异常
        IllegalStateException exception = assertThrows(IllegalStateException.class, 
            () -> properties.validateConfiguration());
        assertTrue(exception.getMessage().contains("至少需要配置一个存储桶"));
    }
    
    @Test
    @DisplayName("空用户配置异常测试")
    void testEmptyUsersException() {
        // 创建存储桶配置但没有用户配置
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName(TEST_BUCKET);
        bucketConfig.setStoragePath(TEST_STORAGE_PATH);
        bucketConfig.setMaxFileSize(104857600L);
        bucketConfig.setDefaultChunkSize(2097152);
        
        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put(TEST_BUCKET, bucketConfig);
        properties.setBuckets(buckets);
        
        // 验证应该抛出异常
        IllegalStateException exception = assertThrows(IllegalStateException.class, 
            () -> properties.validateConfiguration());
        assertTrue(exception.getMessage().contains("至少需要配置一个用户"));
    }
    
    @Test
    @DisplayName("存储桶名称为空异常测试")
    void testEmptyBucketNameException() {
        // 创建存储桶名称为空的配置
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName(""); // 空名称
        bucketConfig.setStoragePath(TEST_STORAGE_PATH);
        bucketConfig.setMaxFileSize(104857600L);
        bucketConfig.setDefaultChunkSize(2097152);
        
        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put(TEST_BUCKET, bucketConfig);
        properties.setBuckets(buckets);
        
        // 创建用户配置
        FileTransferProperties.UserConfig userConfig = new FileTransferProperties.UserConfig();
        userConfig.setSecretKey(TEST_SECRET_KEY);
        userConfig.setAllowedBuckets(new String[]{TEST_BUCKET});
        
        Map<String, FileTransferProperties.UserConfig> users = new HashMap<>();
        users.put(TEST_USER, userConfig);
        properties.setUsers(users);
        
        // 验证应该抛出异常
        IllegalStateException exception = assertThrows(IllegalStateException.class, 
            () -> properties.validateConfiguration());
        assertTrue(exception.getMessage().contains("存储桶名称不能为空"));
    }
    
    @Test
    @DisplayName("用户密钥为空异常测试")
    void testEmptyUserSecretKeyException() {
        // 创建存储桶配置
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName(TEST_BUCKET);
        bucketConfig.setStoragePath(TEST_STORAGE_PATH);
        bucketConfig.setMaxFileSize(104857600L);
        bucketConfig.setDefaultChunkSize(2097152);
        
        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put(TEST_BUCKET, bucketConfig);
        properties.setBuckets(buckets);
        
        // 创建用户配置但密钥为空
        FileTransferProperties.UserConfig userConfig = new FileTransferProperties.UserConfig();
        userConfig.setSecretKey(""); // 空密钥
        userConfig.setAllowedBuckets(new String[]{TEST_BUCKET});
        
        Map<String, FileTransferProperties.UserConfig> users = new HashMap<>();
        users.put(TEST_USER, userConfig);
        properties.setUsers(users);
        
        // 验证应该抛出异常
        IllegalStateException exception = assertThrows(IllegalStateException.class, 
            () -> properties.validateConfiguration());
        assertTrue(exception.getMessage().contains("用户密钥不能为空"));
    }
    
    @Test
    @DisplayName("用户访问不存在存储桶异常测试")
    void testUserAccessNonExistentBucketException() {
        // 创建存储桶配置
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName(TEST_BUCKET);
        bucketConfig.setStoragePath(TEST_STORAGE_PATH);
        bucketConfig.setMaxFileSize(104857600L);
        bucketConfig.setDefaultChunkSize(2097152);
        
        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put(TEST_BUCKET, bucketConfig);
        properties.setBuckets(buckets);
        
        // 创建用户配置，允许访问不存在的存储桶
        FileTransferProperties.UserConfig userConfig = new FileTransferProperties.UserConfig();
        userConfig.setSecretKey(TEST_SECRET_KEY);
        userConfig.setAllowedBuckets(new String[]{"non-existent-bucket"});
        
        Map<String, FileTransferProperties.UserConfig> users = new HashMap<>();
        users.put(TEST_USER, userConfig);
        properties.setUsers(users);
        
        // 验证应该抛出异常
        IllegalStateException exception = assertThrows(IllegalStateException.class, 
            () -> properties.validateConfiguration());
        assertTrue(exception.getMessage().contains("配置的存储桶不存在"));
    }
    
    @Test
    @DisplayName("默认配置获取测试")
    void testGetDefaultConfig() {
        // 测试没有存储桶时的默认配置
        FileTransferProperties.BucketConfig defaultConfig = properties.getDefaultConfig();
        assertNotNull(defaultConfig);
        assertEquals("default", defaultConfig.getName());
        assertTrue(defaultConfig.getMaxFileSize() > 0);
        assertTrue(defaultConfig.getDefaultChunkSize() > 0);
        
        // 添加存储桶配置后测试
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName(TEST_BUCKET);
        bucketConfig.setStoragePath(TEST_STORAGE_PATH);
        bucketConfig.setMaxFileSize(104857600L);
        bucketConfig.setDefaultChunkSize(2097152);
        
        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put(TEST_BUCKET, bucketConfig);
        properties.setBuckets(buckets);
        
        // 应该返回第一个存储桶配置
        FileTransferProperties.BucketConfig actualDefault = properties.getDefaultConfig();
        assertEquals(TEST_BUCKET, actualDefault.getName());
        assertEquals(TEST_STORAGE_PATH, actualDefault.getStoragePath());
    }
    
    @Test
    @DisplayName("有效配置获取测试")
    void testGetEffectiveConfig() {
        // 创建存储桶配置
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName(TEST_BUCKET);
        bucketConfig.setStoragePath(TEST_STORAGE_PATH);
        bucketConfig.setUploadRateLimit(10485760L); // 10MB/s
        bucketConfig.setMaxFileSize(104857600L); // 100MB
        bucketConfig.setDefaultChunkSize(2097152); // 2MB
        
        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put(TEST_BUCKET, bucketConfig);
        properties.setBuckets(buckets);
        
        // 创建用户配置，覆盖部分存储桶配置
        FileTransferProperties.UserConfig userConfig = new FileTransferProperties.UserConfig();
        userConfig.setSecretKey(TEST_SECRET_KEY);
        userConfig.setAllowedBuckets(new String[]{TEST_BUCKET});
        userConfig.setUploadRateLimit(5242880L); // 5MB/s，覆盖存储桶配置
        userConfig.setMaxFileSize(52428800L); // 50MB，覆盖存储桶配置
        
        Map<String, FileTransferProperties.UserConfig> users = new HashMap<>();
        users.put(TEST_USER, userConfig);
        properties.setUsers(users);
        
        // 获取有效配置
        FileTransferProperties.EffectiveConfig effectiveConfig = 
            properties.getEffectiveConfig(TEST_USER, TEST_BUCKET);
        
        // 验证配置合并结果
        assertNotNull(effectiveConfig);
        assertEquals(TEST_STORAGE_PATH, effectiveConfig.getStoragePath()); // 来自存储桶配置
        assertEquals(5242880L, effectiveConfig.getUploadRateLimit()); // 来自用户配置（覆盖）
        assertEquals(52428800L, effectiveConfig.getMaxFileSize()); // 来自用户配置（覆盖）
        assertEquals(2097152, effectiveConfig.getDefaultChunkSize()); // 来自存储桶配置
    }
    
    @Test
    @DisplayName("用户权限检查测试")
    void testUserBucketPermission() {
        // 创建存储桶配置
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName(TEST_BUCKET);
        bucketConfig.setStoragePath(TEST_STORAGE_PATH);
        
        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put(TEST_BUCKET, bucketConfig);
        properties.setBuckets(buckets);
        
        // 创建用户配置
        FileTransferProperties.UserConfig userConfig = new FileTransferProperties.UserConfig();
        userConfig.setSecretKey(TEST_SECRET_KEY);
        userConfig.setAllowedBuckets(new String[]{TEST_BUCKET});
        
        Map<String, FileTransferProperties.UserConfig> users = new HashMap<>();
        users.put(TEST_USER, userConfig);
        properties.setUsers(users);
        
        // 测试权限检查
        assertTrue(properties.isUserAllowedToBucket(TEST_USER, TEST_BUCKET));
        assertFalse(properties.isUserAllowedToBucket(TEST_USER, "other-bucket"));
        assertFalse(properties.isUserAllowedToBucket("other-user", TEST_BUCKET));
    }
}
