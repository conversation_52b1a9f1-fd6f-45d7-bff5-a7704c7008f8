package com.sdesrd.filetransfer.server.service;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

/**
 * 简单的存储路径测试
 * 独立测试新的存储路径逻辑，不依赖其他组件
 */
@DisplayName("简单存储路径测试")
class SimpleStoragePathTest {
    
    // === 常量定义 ===
    
    /**
     * 路径分隔符
     */
    private static final String PATH_SEPARATOR = "/";
    
    /**
     * 危险字符列表
     */
    private static final String[] DANGEROUS_CHARS = {"<", ">", ":", "\"", "|", "?", "*"};
    
    /**
     * 文件ID最小长度
     */
    private static final int MIN_FILE_ID_LENGTH = 4;
    
    // === 测试组件 ===
    
    @TempDir
    Path tempDir;
    
    private String bucketStoragePath;
    
    @BeforeEach
    void setUp() throws IOException {
        bucketStoragePath = tempDir.resolve("buckets").resolve("test-bucket").toString();
        Files.createDirectories(Path.of(bucketStoragePath));
        System.out.println("[测试初始化] 存储桶路径: " + bucketStoragePath);
    }
    
    @Test
    @DisplayName("新存储路径格式测试")
    void testNewStoragePathFormat() {
        System.out.println("\n=== 测试：新存储路径格式 ===");
        
        String[] testKeys = {
            "simple-file.txt",
            "reports/2024/annual.pdf",
            "images/photos/vacation.jpg",
            "data/users/user123/profile.json"
        };
        
        for (String key : testKeys) {
            // 新的存储路径格式：${bucket.storage-path}/{key}
            String newPath = buildNewStoragePath(bucketStoragePath, key);
            String expectedPath = bucketStoragePath + PATH_SEPARATOR + key;
            
            assertEquals(expectedPath, newPath, "新路径格式不匹配，key: " + key);
            
            System.out.println("[路径验证] key: " + key);
            System.out.println("  新路径: " + newPath);
            System.out.println("  验证结果: ✓ 通过");
        }
        
        System.out.println("✅ 新存储路径格式测试通过");
    }
    
    @Test
    @DisplayName("旧存储路径格式对比测试")
    void testOldVsNewStoragePathComparison() {
        System.out.println("\n=== 测试：旧 vs 新存储路径格式对比 ===");
        
        String fileId = "abcd1234567890ef";
        String fileName = "test-document.pdf";
        String key = "reports/2024/annual-report.pdf";
        
        // 旧的存储路径格式：${storage-path}/${fileId前4位}/${fileId}/${fileName}
        String oldPath = buildOldStoragePath(bucketStoragePath, fileId, fileName);
        
        // 新的存储路径格式：${bucket.storage-path}/{key}
        String newPath = buildNewStoragePath(bucketStoragePath, key);
        
        System.out.println("[路径对比]");
        System.out.println("  旧路径格式: " + oldPath);
        System.out.println("  新路径格式: " + newPath);
        
        // 计算路径层级
        int oldLevels = oldPath.split(PATH_SEPARATOR).length - bucketStoragePath.split(PATH_SEPARATOR).length;
        int newLevels = newPath.split(PATH_SEPARATOR).length - bucketStoragePath.split(PATH_SEPARATOR).length;
        
        System.out.println("  旧路径层级: " + oldLevels);
        System.out.println("  新路径层级: " + newLevels);
        
        // 验证新路径的优势
        assertTrue(newPath.contains(key), "新路径应该包含完整的key");
        assertFalse(newPath.contains(fileId), "新路径不应该包含fileId");
        assertTrue(newPath.contains("reports/2024"), "新路径应该保持目录结构的可读性");
        
        System.out.println("✅ 路径格式对比测试通过");
    }
    
    @Test
    @DisplayName("路径安全性验证测试")
    void testPathSecurityValidation() {
        System.out.println("\n=== 测试：路径安全性验证 ===");
        
        // 测试危险路径
        String[] dangerousKeys = {
            "../../../etc/passwd",      // 路径遍历
            "/etc/passwd",              // 绝对路径
            "C:\\Windows\\System32",    // Windows驱动器路径
            "file<name>.txt",           // 危险字符
            "file>name.txt",
            "file:name.txt",
            "file\"name.txt",
            "file|name.txt",
            "file?name.txt",
            "file*name.txt"
        };
        
        for (String dangerousKey : dangerousKeys) {
            System.out.println("[安全测试] 测试危险key: " + dangerousKey);
            
            Exception exception = assertThrows(IllegalArgumentException.class, () -> {
                validateKeyForPath(dangerousKey);
            }, "应该拒绝危险key: " + dangerousKey);
            
            System.out.println("  异常信息: " + exception.getMessage());
            System.out.println("  验证结果: ✓ 正确拒绝");
        }
        
        System.out.println("✅ 路径安全性验证测试通过");
    }
    
    @Test
    @DisplayName("文件创建和访问测试")
    void testFileCreationAndAccess() throws Exception {
        System.out.println("\n=== 测试：文件创建和访问 ===");
        
        String[] testKeys = {
            "simple-file.txt",
            "reports/2024/annual.pdf",
            "images/photos/vacation.jpg"
        };
        
        String testContent = "这是测试文件内容，用于验证新的存储路径逻辑。";
        
        for (String key : testKeys) {
            // 构建文件路径
            String filePath = buildNewStoragePath(bucketStoragePath, key);
            File file = new File(filePath);
            
            // 创建父目录
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                assertTrue(created, "无法创建父目录: " + parentDir.getAbsolutePath());
            }
            
            // 创建测试文件
            Files.write(file.toPath(), testContent.getBytes("UTF-8"));
            
            // 验证文件存在
            assertTrue(file.exists(), "文件应该存在: " + filePath);
            
            // 验证文件内容
            String actualContent = new String(Files.readAllBytes(file.toPath()), "UTF-8");
            assertEquals(testContent, actualContent, "文件内容不匹配");
            
            System.out.println("[文件操作] key: " + key);
            System.out.println("  文件路径: " + filePath);
            System.out.println("  文件大小: " + file.length() + " bytes");
            System.out.println("  验证结果: ✓ 创建和读取成功");
        }
        
        System.out.println("✅ 文件创建和访问测试通过");
    }
    
    @Test
    @DisplayName("中文路径支持测试")
    void testChinesePathSupport() throws Exception {
        System.out.println("\n=== 测试：中文路径支持 ===");
        
        String[] chineseKeys = {
            "文档/报告/年度总结.pdf",
            "图片/照片/风景照.jpg",
            "数据/用户/张三/个人资料.json"
        };
        
        String testContent = "中文内容测试";
        
        for (String key : chineseKeys) {
            String filePath = buildNewStoragePath(bucketStoragePath, key);
            File file = new File(filePath);
            
            // 创建父目录
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                assertTrue(created, "无法创建中文目录: " + parentDir.getAbsolutePath());
            }
            
            // 创建文件
            Files.write(file.toPath(), testContent.getBytes("UTF-8"));
            assertTrue(file.exists(), "中文路径文件应该存在");
            
            // 验证内容
            String actualContent = new String(Files.readAllBytes(file.toPath()), "UTF-8");
            assertEquals(testContent, actualContent, "中文文件内容不匹配");
            
            System.out.println("[中文路径] key: " + key);
            System.out.println("  文件路径: " + filePath);
            System.out.println("  验证结果: ✓ 支持中文路径");
        }
        
        System.out.println("✅ 中文路径支持测试通过");
    }
    
    // === 辅助方法 ===
    
    /**
     * 构建新的存储路径
     * 新的存储路径格式：${bucket.storage-path}/{key}
     */
    private String buildNewStoragePath(String bucketPath, String key) {
        validateKeyForPath(key);
        return bucketPath + PATH_SEPARATOR + key;
    }
    
    /**
     * 构建旧的存储路径（用于对比）
     * 旧的存储路径格式：${storage-path}/${fileId前4位}/${fileId}/${fileName}
     */
    private String buildOldStoragePath(String storagePath, String fileId, String fileName) {
        return storagePath + PATH_SEPARATOR + 
               fileId.substring(0, Math.min(MIN_FILE_ID_LENGTH, fileId.length())) + PATH_SEPARATOR + 
               fileId + PATH_SEPARATOR + 
               fileName;
    }
    
    /**
     * 验证key用于路径构建的安全性
     */
    private void validateKeyForPath(String key) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("对象key不能为空");
        }
        
        // 检查路径遍历攻击
        if (key.contains("..")) {
            throw new IllegalArgumentException("对象key不能包含..路径");
        }
        
        // 检查绝对路径
        if (key.startsWith("/") || key.startsWith("\\")) {
            throw new IllegalArgumentException("对象key不能以路径分隔符开头");
        }
        
        // 检查Windows驱动器路径
        if (key.matches("^[A-Za-z]:.*")) {
            throw new IllegalArgumentException("对象key不能包含驱动器路径");
        }
        
        // 检查其他危险字符
        for (String dangerousChar : DANGEROUS_CHARS) {
            if (key.contains(dangerousChar)) {
                throw new IllegalArgumentException("对象key不能包含危险字符: " + dangerousChar);
            }
        }
    }
}
